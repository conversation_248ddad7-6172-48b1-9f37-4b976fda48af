
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证码临时查询</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --error-color: #dc3545;
            --success-color: #28a745;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        .container:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 25px;
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="email"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.2);
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #3a56b8;
        }
        
        button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 25px;
            padding: 15px;
            border-radius: 6px;
            background-color: var(--secondary-color);
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .result h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .result p {
            margin-bottom: 5px;
        }
        
        .error {
            color: var(--error-color);
        }
        
        .success {
            color: var(--success-color);
        }
        
        .loader {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .loader.show {
            display: block;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        @media (max-width: 576px) {
            .container {
                padding: 20px;
            }
        }
        .tips {
            margin-top: 20px;
            text-align: center;
            
        }
        
        .tips img {
            width: 400px;
            margin-top: 20px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码接码查询</h1>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="email">请输入邮箱地址</label>
                <input type="email" id="email" name="email" placeholder="例如: <EMAIL>" required>
            </div>
            
            <button type="submit" id="submitBtn">查询</button>
        </form>
        
        <div class="loader" id="loader">
            <div class="spinner"></div>
            <p>正在查询中...</p>
        </div>
        
        <div class="result" id="result">
            <h3>查询结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('emailForm');
            const loader = document.getElementById('loader');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const submitBtn = document.getElementById('submitBtn');
            
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value.trim();
                
                if (!email) {
                    showError('请输入有效的邮箱地址');
                    return;
                }
                
                // 显示加载状态
                loader.classList.add('show');
                result.classList.remove('show');
                submitBtn.disabled = true;
                
                try {
                    const response = await fetch(`https://api.12050231.xyz/api/windsurf?email=${encodeURIComponent(email)}`);
                    
                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }
                    
                    const data = await response.json();
                    
                    // 隐藏加载状态
                    loader.classList.remove('show');
                    result.classList.add('show');
                    submitBtn.disabled = false;
                    
                    if (data.success) {
                        let resultHtml = `<p>${data.summary}</p>`;
                        
                        if (data.verificationCode) {
                            resultHtml += `<p>验证码: <strong class="success">${data.verificationCode}</strong></p>`;
                        }

                        

                        if (data.tips) {
                            resultHtml += `<p><strong class="">${data.tips}</strong></p>`;
                        }
                        resultContent.innerHTML = resultHtml;
                        try {
                            // await navigator.clipboard.writeText(data.verificationCode);
                        } catch (error) {
                            
                        }
                        
                    } else {
                        showError('查询失败: ' + (data.message || '未知错误'));
                    }
                } catch (error) {
                    // 隐藏加载状态
                    loader.classList.remove('show');
                    submitBtn.disabled = false;
                    
                    showError('发生错误: ' + error.message);
                }
            });
            
            function showError(message) {
                result.classList.add('show');
                resultContent.innerHTML = `<p class="error">${message}</p>`;
            }
        });
    </script>
</body>
</html>
