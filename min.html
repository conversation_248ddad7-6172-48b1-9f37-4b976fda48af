<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <link rel="icon" href="https://www.suning.com/favicon.ico" />
    <meta name="viewport"

        <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
    <title>混淆</title>

</head>
<script src="https://proxy.nanwang.de/https://cdn.jsdelivr.net/npm/javascript-obfuscator/dist/index.browser.js"></script>
<style>
    html{font-size: 50px;}
    body{line-height:1.5;color:#353d44;font-family:Arial, Helvetica, STHeiTi, sans-serif;background:#F2F2F2;}
    .tc{text-align:center;}

textarea{
    width: 1000px;padding: 10px;;margin:10px;border: 1px solid #ccc;font-size:14px;min-height: 300px;
    border-radius: 7px;;
}
button{padding: 20px 40px;background: #007bff;color: #fff;border: none;border-radius: 7px;cursor: pointer;font-size: 20px;;}
.code{
    width: 1000px;
    background:#fff;
    padding: 10px;;
    font-size: 12px;
    border-radius: 7px;;
    min-height: 400px;
}

</style>
<body style="width:1000px;margin: 0 auto;">
    
    <textarea name="" id="" ></textarea>
    <div class="tc">
        <button onclick="change()">混淆</button>
    </div>
   
    <textarea name="" id="" class="code"></textarea>
    
<script>
    function change(params) {
            
        var obfuscationResult = JavaScriptObfuscator.obfuscate(
            document.querySelector("textarea").value,
            {
                compact: true,
                controlFlowFlattening: false,
                deadCodeInjection: false,
                debugProtection: false,
                debugProtectionInterval: 0,
                disableConsoleOutput: false,
                identifierNamesGenerator: 'hexadecimal',
                log: false,
                numbersToExpressions: false,
                renameGlobals: false,
                selfDefending: false,
                simplify: true,
                splitStrings: false,
                stringArray: true,
                stringArrayCallsTransform: false,
                stringArrayCallsTransformThreshold: 0.5,
                stringArrayEncoding: [],
                stringArrayIndexShift: true,
                stringArrayRotate: true,
                stringArrayShuffle: true,
                stringArrayWrappersCount: 1,
                stringArrayWrappersChainedCalls: true,
                stringArrayWrappersParametersMaxCount: 2,
                stringArrayWrappersType: 'variable',
                stringArrayThreshold: 0.75,
                unicodeEscapeSequence: false
            }
        );

        console.log(obfuscationResult.getObfuscatedCode());

        document.querySelector(".code").value = obfuscationResult.getObfuscatedCode();

    }



</script>


</body>

</html>