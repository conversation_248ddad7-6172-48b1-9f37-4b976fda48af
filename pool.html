<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://www.ppanda.com/images/logo-frog-all.png" type="image/x-icon">
    <title>Augment Code 号池</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .content {
            padding: 24px;
        }

        .button-container {
            text-align: center;
            margin-bottom: 24px;
        }

        #fetchButton {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        #fetchButton:hover:not(:disabled) {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        #fetchButton:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            color: #718096;
            margin: 20px 0;
            font-size: 14px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .account-info {
            display: none;
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
        }

        .account-item {
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .account-item:last-child {
            border-bottom: none;
        }

        .account-key {
            font-weight: 500;
            color: #2d3748;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .account-value {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .value-text {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            font-size: 13px;
            color: #4a5568;
            flex: 1;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .copy-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .copy-btn:hover {
            background: #38a169;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 16px;
            border-radius: 8px;
            font-size: 14px;
            display: none;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            opacity: 0;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .tips {
            margin-top: 24px;
            padding: 16px;
            background: #f7fafc;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
        }

        /* Usage 信息相关样式 - 重新设计 */
        .usage-info {
            font-size: 13px;
            line-height: 1.4;
        }

        .usage-container {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .usage-error {
            color: #e53e3e;
            font-weight: 600;
            padding: 16px;
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            border-radius: 8px;
            border-left: 4px solid #e53e3e;
            margin: 8px 0;
        }

        .usage-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            margin: 0;
        }

        .token-header {
            padding: 12px 16px;
            margin: 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 13px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .token-header-success {
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            color: #2d3748;
        }

        .token-header-error {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            color: #2d3748;
        }

        .token-status-success {
            color: #38a169;
            font-weight: 700;
            font-size: 12px;
        }

        .token-status-error {
            color: #e53e3e;
            font-weight: 700;
            font-size: 12px;
        }

        .token-result-container {
            padding: 16px;
            font-size: 13px;
            line-height: 1.4;
            background: #fafafa;
        }

        .balance-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .balance-amount {
            font-size: 24px;
            font-weight: 700;
            margin-left: 8px;
        }

        .credit-blocks-header {
            margin-bottom: 12px;
            font-size: 14px;
            color: #4a5568;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }

        .credit-blocks-container {
            display: grid;
            gap: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 16px;
            padding-right: 4px;
        }

        .credit-blocks-container::-webkit-scrollbar {
            width: 6px;
        }

        .credit-blocks-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .credit-blocks-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .credit-blocks-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .credit-block {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .credit-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--block-color);
        }

        .credit-block-active {
            --block-color: #38a169;
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border-color: #9ae6b4;
            box-shadow: 0 2px 8px rgba(56, 161, 105, 0.1);
        }

        .credit-block-inactive {
            --block-color: #a0aec0;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-color: #e2e8f0;
        }

        .credit-block:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .credit-block-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .credit-block-balance-active {
            color: #38a169;
            font-weight: 700;
            font-size: 16px;
        }

        .credit-block-balance-inactive {
            color: #a0aec0;
            font-weight: 600;
            font-size: 16px;
        }

        .credit-block-details {
            font-size: 12px;
            color: #718096;
            line-height: 1.4;
            background: rgba(255,255,255,0.7);
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }

        .token-error-container {
            padding: 16px;
            background: #fff5f5;
            font-size: 13px;
        }

        .error-message {
            color: #e53e3e;
            font-weight: 600;
        }

        .usage-url-info {
            margin-top: 12px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.8);
            border-radius: 6px;
            font-size: 11px;
            color: #718096;
            word-break: break-all;
            border: 1px solid #e2e8f0;
        }

        .full-response-details {
            margin-top: 16px;
            border-top: 1px solid #e2e8f0;
            padding-top: 12px;
        }

        .full-response-summary {
            cursor: pointer;
            color: #667eea;
            font-size: 12px;
            font-weight: 600;
            padding: 8px 12px;
            background: #f7fafc;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            display: inline-block;
        }

        .full-response-summary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .full-response-content {
            margin-top: 8px;
            padding: 12px;
            background: #2d3748;
            border-radius: 6px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #4a5568;
        }

        .full-response-pre {
            font-size: 11px;
            margin: 0;
            background: none;
            padding: 0;
            white-space: pre-wrap;
            word-break: break-word;
            color: #e2e8f0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        /* 总余额显示样式 */
        .total-balance-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            margin: 8px 0;
        }

        .total-balance-amount {
            font-size: 28px;
            font-weight: 700;
            margin-left: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img src="https://www.ppanda.com/images/logo-frog-all.png" width="30px" alt=""> Augment Code 号池</h1>
        </div>

        <div class="content">
            <div class="button-container">
                <button id="fetchButton" onclick="fetchData()">随机获取一个账号</button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                正在获取数据...
            </div>

            <div class="error-message" id="errorMessage"></div>

            <div class="account-info" id="accountInfo"></div>

            <div class="tips">
                会用的自取使用，不会用的可以无视，站长拥有一切解释权
            </div>
            <div style="margin-top: 32px; padding: 12px 0; text-align: center; font-size: 12px; color: #a0aec0;">
                本站总访问量 <span id="busuanzi_value_site_pv" style="font-weight: 600; color: #667eea;">-</span> 次
            </div>
        </div>
    </div>

    <div class="toast" id="toast">复制成功！</div>

    <script>
        async function fetchData() {
            const button = document.getElementById('fetchButton');
            const loading = document.getElementById('loading');
            const accountInfo = document.getElementById('accountInfo');
            const errorMessage = document.getElementById('errorMessage');

            // 重置状态
            button.disabled = true;
            loading.style.display = 'block';
            accountInfo.style.display = 'none';
            errorMessage.style.display = 'none';

            try {
                const response = await fetch('https://account.767700.xyz?token=UBmkmNhDBb');

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                const responseData = await response.json();

                // 检查响应是否成功
                if (!responseData.success) {
                    throw new Error('API 返回失败状态');
                }

                // 获取 result 的值
                if (!responseData.result) {
                    throw new Error('返回数据格式错误：缺少 result 字段');
                }

                // 直接使用 result 对象，不再需要解析 JSON 字符串
                const selectedAccount = responseData.result;

                // 显示账号信息
                displayAccount(selectedAccount);

                // 然后异步获取 useage 信息
                if (responseData.useage) {
                    // 显示正在加载 useage 的状态
                    updateUsageStatus('处理中...');

                    try {
                        // 解析 useage 数组 - 现在是字符串格式的 JSON 数组
                        let useageUrls = [];
                        if (typeof responseData.useage === 'string') {
                            useageUrls = JSON.parse(responseData.useage);
                        } else if (Array.isArray(responseData.useage)) {
                            useageUrls = responseData.useage;
                        } else {
                            throw new Error('useage 格式不正确');
                        }

                        if (useageUrls.length > 0) {
                            // 处理所有 URL
                            await fetchUsageFromUrlsProgressive(useageUrls);
                        } else {
                            updateUsageStatus('useage 数组为空');
                        }
                    } catch (error) {
                        updateUsageStatus(`获取失败: ${error.message}`);
                    }
                }

            } catch (error) {
                // 显示错误信息
                errorMessage.textContent = `获取数据失败: ${error.message}`;
                errorMessage.style.display = 'block';
            } finally {
                // 恢复按钮状态并隐藏加载状态
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 更新 useage 状态显示的函数
        function updateUsageStatus(message) {
            const accountInfo = document.getElementById('accountInfo');
            let usageDiv = accountInfo.querySelector('.useage-status');

            if (!usageDiv) {
                // 如果还没有 useage 状态区域，创建一个
                usageDiv = document.createElement('div');
                usageDiv.className = 'account-item';
                usageDiv.innerHTML = `
                    <div class="account-key">useage:</div>
                    <div class="account-value">
                        <div class="useage-status">${message}</div>
                    </div>
                `;
                accountInfo.appendChild(usageDiv);
            } else {
                // 更新现有的状态
                usageDiv.innerHTML = message;
            }
        }

        // 处理 useage URL 数组的函数
        async function fetchUsageFromUrlsProgressive(useageUrls) {
            const useageResults = [];
            let totalBalance = 0;

            for (let i = 0; i < useageUrls.length; i++) {
                const url = useageUrls[i];

                try {
                    const useageData = await fetchUsageDetail(url);

                    const result = {
                        index: i + 1,
                        url: url,
                        data: useageData,
                        success: true
                    };
                    useageResults.push(result);

                    // 累加余额
                    const creditsBalance = parseFloat(useageData.credits_balance) || 0;
                    totalBalance += creditsBalance;

                } catch (error) {
                    useageResults.push({
                        index: i + 1,
                        url: url,
                        error: error.message,
                        success: false
                    });
                }

                // 添加延迟避免请求过快
                if (i < useageUrls.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            // 计算完成后统一显示最终总余额
            displayFinalUsageBalance(totalBalance, useageResults);
        }

        // 显示最终 useage 总余额的函数
        function displayFinalUsageBalance(totalBalance, results) {
            const accountInfo = document.getElementById('accountInfo');
            let usageDiv = accountInfo.querySelector('.account-item:last-child');

            // 如果最后一个元素是 useage 状态，替换它
            if (usageDiv && usageDiv.querySelector('.account-key').textContent === 'useage:') {
                usageDiv.remove();
            }

            // 创建新的 useage 显示元素
            const newUsageDiv = document.createElement('div');
            newUsageDiv.className = 'account-item';
            newUsageDiv.innerHTML = `
                <div class="account-key">useage:</div>
                <div class="account-value">
                    <div class="">
                        <div class="total-balance-display">
                            <strong>总余额:</strong> <span class="total-balance-amount">${totalBalance.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;

            accountInfo.appendChild(newUsageDiv);
        }

        // 获取单个 useage URL 的详细信息
        async function fetchUsageDetail(url) {
            try {
                const cleanUrl = url.trim();
                const requestUrl = `https://checkvip.767700.xyz?token=${encodeURIComponent(cleanUrl)}`;

                const response = await fetch(requestUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`请求失败: ${response.status} ${response.statusText}\n${errorText}`);
                }

                const result = await response.json();

                // 检查返回的结果
                if (result.error) {
                    throw new Error(`API 错误: ${result.error}${result.message ? ' - ' + result.message : ''}`);
                }

                if (!result.ok) {
                    throw new Error(`API 请求失败: ${result.status} ${result.statusText || result.error || '未知错误'}`);
                }

                return result.data;

            } catch (error) {
                throw error;
            }
        }

        function displayAccount(account) {
            const accountInfo = document.getElementById('accountInfo');
            accountInfo.innerHTML = '';

            // 遍历账号对象的所有属性
            Object.entries(account).forEach(([key, value]) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'account-item';

                itemDiv.innerHTML = `
                    <div class="account-key">${key}:</div>
                    <div class="account-value">
                        <div class="value-text">${value}</div>
                        <button class="copy-btn" onclick="copyToClipboard('${value.replace(/'/g, "\\'")}')">复制</button>
                    </div>
                `;

                accountInfo.appendChild(itemDiv);
            });

            accountInfo.style.display = 'block';
        }

        async function copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                showToast();
            } catch (err) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast();
            }
        }

        function showToast() {
            const toast = document.getElementById('toast');

            // 重置状态
            toast.classList.remove('show');

            // 强制重绘
            toast.offsetHeight;

            // 显示toast
            toast.classList.add('show');

            // 2秒后隐藏
            setTimeout(() => {
                toast.classList.remove('show');
            }, 2000);
        }
    </script>
    <script async src="https://busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script>
    
</body>
</html>

