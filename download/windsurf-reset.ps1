# reset-id.ps1
# 自动修改 Windsurf 的机器识别 ID 信息

# 构建配置文件路径
$filePath = "$env:USERPROFILE\AppData\Roaming\Windsurf\User\globalStorage\storage.json"

if (-Not (Test-Path $filePath)) {
    Write-Host "⚠️ 配置文件未找到: $filePath"
    Write-Host "请确认 Windsurf 已运行过，或手动创建该文件后重试。"
    exit 1
}

$json = Get-Content $filePath -Raw | ConvertFrom-Json

if (-Not $json.telemetry) {
    $json | Add-Member -MemberType NoteProperty -Name telemetry -Value (@{})
}

$machineId = [guid]::NewGuid().ToString("N")
$sqmId = "{" + ([guid]::NewGuid().ToString().ToUpper()) + "}"
$devDeviceId = [guid]::NewGuid().ToString()

$json.telemetry.machineId = $machineId
$json.telemetry.sqmId = $sqmId
$json.telemetry.devDeviceId = $devDeviceId

$json | ConvertTo-Json -Depth 10 | Set-Content $filePath -Encoding UTF8

Write-Host "✅ 机器ID已重置"
