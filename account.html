<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
        />
        <title>Cursor Pro 体验账号池</title>
        <style>
            body {
                font-family:
                    -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
                    "Helvetica Neue", Arial, sans-serif;
                text-align: center;
                background-color: #f0f0f0;
            }
            .card {
                background: white;
                padding: 32px;
                border-radius: 16px;
                box-shadow:
                    0 10px 25px rgba(0, 0, 0, 0.1),
                    0 5px 10px rgba(0, 0, 0, 0.05);
                text-align: center;
                max-width: 300px;
                margin: 0 auto 32px auto;
                transition: all 0.3s ease;
                border: 1px solid rgba(24, 144, 255, 0.1);
            }
            .card:hover {
                transform: translateY(-5px);
                box-shadow:
                    0 15px 30px rgba(0, 0, 0, 0.15),
                    0 10px 15px rgba(0, 0, 0, 0.07);
            }
            .button {
                background: linear-gradient(to right, #1890ff, #096dd9);
                color: white;
                border: none;
                padding: 14px 28px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 18px;
                font-weight: 600;
                width: 100%;
                transition: all 0.3s ease;
                letter-spacing: 0.5px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                box-shadow:
                    0 4px 6px rgba(24, 144, 255, 0.25),
                    0 0 0 3px rgba(24, 144, 255, 0.1);
                position: relative;
                overflow: hidden;
            }
            .button:hover {
                background: linear-gradient(to right, #40a9ff, #1890ff);
                transform: translateY(-3px);
                box-shadow:
                    0 7px 14px rgba(24, 144, 255, 0.4),
                    0 0 0 3px rgba(24, 144, 255, 0.2);
            }
            .button:active {
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(24, 144, 255, 0.35);
            }
            .button:disabled {
                background-color: #bfbfbf;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .email {
                margin: 24px auto;
                padding: 16px;
                font-size: 16px;
                color: #333;
                word-break: break-all;
                background-color: #f9f9f9;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
                font-family: monospace;
                font-weight: 500;
                transition: all 0.3s ease;
                white-space: pre-wrap;
                max-width: 500px;
            }
            .email:not(:empty) {
                animation: fadeIn 0.5s ease-out;
            }
            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            .toast {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%) translateY(-100px);
                background-color: rgba(0, 0, 0, 0.85);
                color: white;
                padding: 14px 28px;
                border-radius: 8px;
                transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                opacity: 0;
                z-index: 1000;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                font-weight: 500;
            }
            .toast.show {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                text-align: center;
            }
            .logo-container {
                margin-bottom: 32px;
                text-align: center;
            }
            .logo {
                max-width: 300px;
                height: auto;
                display: block;
                margin: 17px auto;
            }
            .hint {
                color: #666;
                font-size: 14px;
                line-height: 1.6;
                margin: 20px auto;
                padding: 16px;
                background: #f5f5f5;
                border-radius: 8px;
                border-left: 4px solid #1890ff;
                text-align: left;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                max-width: 600px;
            }
            .hint a {
                color: #1890ff;
                text-decoration: none;
            }
            .hint a:hover {
                text-decoration: underline;
            }
            .hint code {
                display: block;
                padding: 12px;
                margin: 8px 0;
                background: #f8f9fa;
                border-radius: 4px;
                white-space: pre-wrap;
                word-break: break-all;
                font-size: 13px;
                font-family: monospace;
                border: 1px solid #e8e8e8;
                color: #1a1a1a;
            }
            .hint strong {
                color: #1890ff;
            }
            .warning-card {
                margin: 20px auto;
                background: #fffbe6;
                border: 1px solid #ffe58f;
                border-radius: 8px;
                padding: 16px;
                max-width: 600px;
            }
            .tips {
                margin-top: 20px;
                text-align: center;
            }

            .tips img {
                width: 400px;
                margin-top: 20px;
                border-radius: 10px;
            }

            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }

            .error {
                color: #dc3545;
            }

            .success {
                color: #28a745;
            }

            /* Tips Section Styles */
            .tips-section {
                max-width: 600px;
                margin: 40px auto 20px auto;
                padding: 0 20px;
            }

            .tips-card {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 20px;
                font-size: 14px;
                line-height: 1.6;
                color: #495057;
                text-align: left;
            }

            .tips-account {
                color: #1890ff;
                display: block;
                margin-bottom: 10px;
            }

            .tips-link {
                color: #1890ff;
                text-decoration: none;
            }

            .tips-text {
                margin-top: 8px;
            }

            /* Tutorial Section Styles */
            .tutorial-section {
                max-width: 800px;
                margin: 40px auto 20px auto;
                padding: 0 20px;
            }

            .tutorial-card {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 30px;
                color: #333;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            .tutorial-title {
                text-align: center;
                margin-bottom: 30px;
                font-size: 24px;
                color: #1890ff;
            }

            .tutorial-steps {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .tutorial-step {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #1890ff;
            }

            .tutorial-step.step-two {
                border-left-color: #28a745;
            }

            .tutorial-step.step-three {
                border-left-color: #ffc107;
            }

            .tutorial-step-title {
                color: #1890ff;
                margin-bottom: 15px;
                font-size: 18px;
            }

            .tutorial-step.step-two .tutorial-step-title {
                color: #28a745;
            }

            .tutorial-step.step-three .tutorial-step-title {
                color: #ffc107;
            }

            .tutorial-text {
                line-height: 1.6;
                margin-bottom: 12px;
                color: #495057;
            }

            .tutorial-text:last-child {
                margin-bottom: 0;
            }

            .tutorial-link {
                color: #1890ff;
                text-decoration: underline;
            }

            .tutorial-strong {
                color: #1890ff;
            }

            .tutorial-footer {
                text-align: center;
                margin-top: 25px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 6px;
                color: #495057;
            }

            .tutorial-footer-text {
                font-size: 14px;
            }

            /* Team Tutorial Styles */
            .team-tutorial-image-container {
                text-align: center;
                margin-bottom: 25px;
            }

            .team-tutorial-image {
                width: 100%;
                max-width: 600px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .team-tutorial-description {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #1890ff;
                line-height: 1.6;
                color: #495057;
            }

            .team-tutorial-description h3 {
                color: #1890ff;
                margin-bottom: 15px;
                font-size: 18px;
            }

            .team-tutorial-description p {
                margin: 0;
            }

            .team-tutorial-description p + p {
                margin-top: 12px;
            }

            /* Arrow Flow Styles */
            .flow-arrow {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin: 30px 0;
                position: relative;
            }

            .flow-arrow::before {
                content: '';
                width: 3px;
                height: 60px;
                background: linear-gradient(to bottom, #1890ff, #40a9ff);
                border-radius: 2px;
            }

            .flow-arrow::after {
                content: '';
                position: absolute;
                bottom: calc(50% - 35px);
                width: 0;
                height: 0;
                border-top: 10px solid #1890ff;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 0.8;
                }
            }

            .combined-tutorial {
                max-width: 800px;
                margin: 40px auto 20px auto;
                padding: 0 20px;
            }

            .combined-tutorial-card {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 30px;
                color: #333;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            .combined-tutorial-title {
                text-align: center;
                margin-bottom: 30px;
                font-size: 24px;
                color: #1890ff;
            }
        </style>
    </head>
    <body>
        <div class="container">


            <div class="card">
                <button
                    id="generateEmailBtn"
                    class="button"
                    onclick="generateRandomEmail()"
                >
                    生成随机邮箱
                </button>
                <div id="randomEmailDisplay" class="email"></div>
            </div>

            <div
                class="card"
                style="max-width: 600px; margin: 0 auto 32px auto"
            >
                <h3 style="margin-bottom: 20px; color: #1890ff">验证码查询</h3>
                <form id="emailForm">
                    <div style="margin-bottom: 15px">
                        <input
                            type="email"
                            id="emailInput"
                            placeholder="请输入邮箱地址获取验证码"
                            style="
                                width: 95%;
                                padding: 14px 18px;
                                border: 1px solid #ddd;
                                border-radius: 6px;
                                font-size: 18px;
                            "
                        />
                    </div>
                    <button
                        type="submit"
                        id="submitBtn"
                        class="button"
                        style="font-size: 18px"
                    >
                        查询
                    </button>
                </form>

                <div
                    class="loader"
                    id="loader"
                    style="display: none; text-align: center; margin: 20px 0"
                >
                    <div
                        style="
                            width: 40px;
                            height: 40px;
                            margin: 0 auto;
                            border: 4px solid rgba(0, 0, 0, 0.1);
                            border-left-color: #1890ff;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                        "
                    ></div>
                    <p style="margin-top: 10px">
                        正在查询中，链路较长，请耐心等待或稍后重试...
                    </p>
                    <p
                        class="quote"
                        style="
                            margin-top: 15px;
                            font-style: italic;
                            color: #666;
                        "
                    ></p>
                </div>

                <div
                    class="result"
                    id="result"
                    style="
                        display: none;
                        margin-top: 20px;
                        padding: 18px;
                        border-radius: 6px;
                        background-color: #f8f9fa;
                        font-size: 17px;
                        word-break: break-all;
                    "
                >
                    <h4 style="margin-bottom: 10px; color: #1890ff">
                        查询结果
                    </h4>
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
        <div id="toast" class="toast"></div>

        <!-- Tips Section -->
        <div class="tips-section">
            <div class="tips-card">
                <strong class="tips-account">账号：<EMAIL></strong>
                <p>官网直接登录（Sign in），然后到 <a href="https://767700.xyz/code?token=E567OUwuWuN" class="tips-link">https://767700.xyz/code?token=E567OUwuWuN</a> 自行接收验证码。虚拟商品，非账号问题售出不退哦。</p>
                <p class="tips-text">可以到官网 <a href="https://app.augmentcode.com/account/subscription" class="tips-link">https://app.augmentcode.com/account/subscription</a> 查询额度</p>
            </div>
        </div>

        <!-- Combined Tutorial Section -->
        <div class="combined-tutorial">
            <div class="combined-tutorial-card">
                <h2 class="combined-tutorial-title">📚 完整教程指南</h2>
                
                <!-- 第一步：账号注册教程 -->
                <div class="tutorial-card" style="margin-bottom: 0; box-shadow: none; border: 1px solid #e9ecef;">
                    <h2 class="tutorial-title">第一步 🎓 账号注册教程</h2>
                    
                    <div class="tutorial-steps">
                        <!-- Step 1 -->
                        <div class="tutorial-step">
                            <h3 class="tutorial-step-title">步骤一：注册 Augment Code 账号</h3>
                            <p class="tutorial-text">
                                访问 <a href="https://www.augmentcode.com/" target="_blank" class="tutorial-link">https://www.augmentcode.com/</a> 
                            </p>
                            <p class="tutorial-text">
                                点击页面右上角的 <strong class="tutorial-strong">"Sign in"</strong> 按钮注册账号
                            </p>
                            <div class="tutorial-text">
                                <p><img src="https://img.262700.xyz/images/2025-07-22-0WSnZ6E5SLt3.png" width="70%" alt=""></p>
                            <div/>
                        </div>
                </div>

                <!-- 流程箭头 -->
                <div class="flow-arrow"></div>

                <!-- 第二步：Team 邀请教程 -->
                <div class="tutorial-card" style="margin-bottom: 0; box-shadow: none; border: 1px solid #e9ecef;">
                    <h2 class="tutorial-title">第二步 🤝 Team 邀请教程</h2>
                    
                    <div class="team-tutorial-image-container">
                        <img src="https://img.262700.xyz/images/2025-07-21-WVPnDqBkA5UY.png" 
                             alt="Team邀请教程" 
                             class="team-tutorial-image">
                    </div>
                    
                    <div class="team-tutorial-description">
                        <h3>最后再用邀请的邮箱重新登录即可，最终生成的账号是邀请的邮箱！！</h3>
                        <img src="https://img.262700.xyz/images/2025-07-22-R0SJez2dfnsF.png" width="90%" alt="">
                    </div>
                </div>
            </div>
        </div>

        <script>
            function showToast(message) {
                const toast = document.getElementById("toast");
                toast.textContent = message;
                toast.classList.add("show");
                setTimeout(() => {
                    toast.classList.remove("show");
                }, 2000);
            }


            function selectText(element) {
                const range = document.createRange();
                range.selectNodeContents(element);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // 第一个邮箱生成函数
            async function generateEmail() {
                await generateEmailCommon(
                    "generateBtn",
                    "emailDisplay",
                    "选择号池邮箱",
                    ` 非第一次使用要重置完机器码，再登录账号哦（接码页下面有重置方案）。

从 Cursor 软件登录，然后选择验证码（Email sign -in code）登录

可到  https://767700.xyz?token=E567OUwuWuN 自行接收验证码，官网可查用量信息。虚拟商品，非账号问题售出不退哦。`,
                );
            }

            // 第二个邮箱生成函数
            async function generateEmail2() {
                await generateEmailCommon(
                    "generateBtn2",
                    "emailDisplay2",
                    "选择 augment 号池",
                    "",
                );
            }

            // 生成随机字符串函数
            function generateRandomString() {
                const button = document.getElementById("generateStringBtn");
                if (button.disabled) return;

                try {
                    button.disabled = true;
                    button.textContent = "生成中...";

                    // 定义字符集：大写字母、小写字母、数字
                    const chars =
                        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
                    let result = "";

                    // 生成12个字符
                    for (let i = 0; i < 12; i++) {
                        result += chars.charAt(
                            Math.floor(Math.random() * chars.length),
                        );
                    }

                    const stringDisplay =
                        document.getElementById("stringDisplay");
                    stringDisplay.textContent = result;

                    // 自动选中生成的字符串
                    selectText(stringDisplay);

                    // 复制到剪贴板
                    navigator.clipboard
                        .writeText(result)
                        .then(() => {
                            showToast("字符串已复制到剪贴板");
                        })
                        .catch(() => {
                            // 如果新 API 失败，尝试使用传统方法
                            const textArea = document.createElement("textarea");
                            textArea.value = result;
                            document.body.appendChild(textArea);
                            textArea.select();

                            try {
                                document.execCommand("copy");
                                showToast("字符串已复制到剪贴板");
                            } catch (execErr) {
                                showToast("复制失败，请手动复制");
                                selectText(stringDisplay);
                            } finally {
                                document.body.removeChild(textArea);
                            }
                        });
                } catch (error) {
                    console.error("Error:", error);
                    showToast("生成失败，请重试");
                } finally {
                    button.disabled = false;
                    button.textContent = "生成随机字符串";
                }
            }

            // 生成随机邮箱函数
            function generateRandomEmail() {
                const button = document.getElementById("generateEmailBtn");
                if (button.disabled) return;

                try {
                    button.disabled = true;
                    button.textContent = "生成中...";

                    // 域名数组
                    const domains = [
                        "001927.xyz",
                        "002627.xyz",
                        "005626.xyz",
                        "005673.xyz",
                        "007772.xyz",
                        "007773.xyz",
                        "007775.xyz",
                        "007778.xyz",
                        "007779.xyz",
                        "008756.xyz",
                        "009567.xyz",
                        "20231131.xyz",
                        "202504137.xyz",
                        "20251727.xyz",
                        "20252525.xyz",
                        "20262626.xyz",
                        "20272727.xyz",
                        "212200.xyz",
                        "252600.xyz",
                        "262700.xyz",
                        "26272829.xyz",
                        "267000.xyz",
                        "272800.xyz",
                        "27282828.xyz",
                        "276000.xyz",
                        "286000.xyz",
                        "293000.xyz",
                        "367000.xyz",
                        "372000.xyz",
                        "386000.xyz",
                        "539000.xyz",
                        "676800.xyz",
                        "697000.xyz",
                        "627000.xyz",
                        "672000.xyz",
                        "673000.xyz",
                        "707100.xyz",
                        "717200.xyz",
                        "725000.xyz",
                        "763000.xyz",
                        "767700.xyz",
                        "785000.xyz",
                        "821000.xyz",
                        "873000.xyz",
                        "827000.xyz",
                        "927000.xyz",
                    ];

                    // 生成12个字符的随机前缀（前6位小写字母，后6位数字）
                    const letters = "abcdefghijklmnopqrstuvwxyz";
                    const numbers = "0123456789";
                    let prefix = "";

                    // 前6位生成小写字母
                    for (let i = 0; i < 6; i++) {
                        prefix += letters.charAt(
                            Math.floor(Math.random() * letters.length),
                        );
                    }

                    // 后6位生成数字
                    for (let i = 0; i < 6; i++) {
                        prefix += numbers.charAt(
                            Math.floor(Math.random() * numbers.length),
                        );
                    }

                    // 随机选择一个域名
                    const randomDomain =
                        domains[Math.floor(Math.random() * domains.length)];

                    // 生成完整的邮箱地址
                    const randomEmail = prefix + "@" + randomDomain;

                    const randomEmailDisplay =
                        document.getElementById("randomEmailDisplay");
                    randomEmailDisplay.textContent = randomEmail;

                    // 自动选中生成的邮箱地址
                    selectText(randomEmailDisplay);

                    // 复制到剪贴板
                    navigator.clipboard
                        .writeText(randomEmail)
                        .then(() => {
                            showToast("邮箱已复制到剪贴板");
                        })
                        .catch(() => {
                            // 如果新 API 失败，尝试使用传统方法
                            const textArea = document.createElement("textarea");
                            textArea.value = randomEmail;
                            document.body.appendChild(textArea);
                            textArea.select();

                            try {
                                document.execCommand("copy");
                                showToast("邮箱已复制到剪贴板");
                            } catch (execErr) {
                                showToast("复制失败，请手动复制");
                                selectText(randomEmailDisplay);
                            } finally {
                                document.body.removeChild(textArea);
                            }
                        });
                } catch (error) {
                    console.error("Error:", error);
                    showToast("生成失败，请重试");
                } finally {
                    button.disabled = false;
                    button.textContent = "生成随机邮箱";
                }
            }

            // 验证码查询功能
            document.addEventListener("DOMContentLoaded", function () {
                const form = document.getElementById("emailForm");
                const loader = document.getElementById("loader");
                const result = document.getElementById("result");
                const resultContent = document.getElementById("resultContent");
                const submitBtn = document.getElementById("submitBtn");

                // 获取URL参数中的token值
                function getTokenFromUrl() {
                    const urlParams = new URLSearchParams(
                        window.location.search,
                    );
                    return urlParams.get("token");
                }

                if (form) {
                    form.addEventListener("submit", async function (e) {
                        e.preventDefault();

                        const email = document
                            .getElementById("emailInput")
                            .value.trim();

                        if (!email) {
                            showError("请输入有效的邮箱地址");
                            return;
                        }

                        // 显示加载状态
                        loader.style.display = "block";
                        result.style.display = "none";
                        submitBtn.disabled = true;

                        // 获取名言
                        try {
                            const quoteResponse = await fetch(
                                "https://read.767700.xyz/",
                            );
                            const quoteData = await quoteResponse.json();
                            if (
                                quoteData.code === 1 &&
                                quoteData.data &&
                                quoteData.data.length > 0
                            ) {
                                const quoteElement =
                                    loader.querySelector(".quote");
                                quoteElement.textContent =
                                    quoteData.data[0].value;
                            }
                        } catch (error) {
                            console.error("获取名言失败:", error);
                        }

                        try {
                            // 获取token参数
                            const token = getTokenFromUrl();

                            // 构建API URL，如果有token则添加到URL中
                            let apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(email)}`;
                            if (token) {
                                apiUrl += `&token=${encodeURIComponent(token)}`;
                            }

                            const response = await fetch(apiUrl);

                            if (!response.ok) {
                                throw new Error(
                                    "未查询到相关邮件，请确认邮箱是否正确，稍后再重新查询",
                                );
                            }

                            const data = await response.json();
                            data.date = new Date(data.date).toLocaleString(
                                "zh-CN",
                                {
                                    timeZone: "Asia/Shanghai",
                                    hour12: false,
                                    month: "long",
                                    day: "numeric",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                },
                            );

                            // 隐藏加载状态
                            loader.style.display = "none";
                            result.style.display = "block";
                            submitBtn.disabled = false;

                            if (data.result) {
                                let resultHtml = ``;

                                if (data.subject) {
                                    resultHtml += `
                                    <h4>${data.subject}</h4>
                                    <div style="margin: 10px 0; font-size: 14px; color: #666;">
                                        发件人：${data.from} | 收件人：${data.to} | 时间：${data.date}
                                    </div>
                                `;
                                }

                                if (data.html) {
                                    resultHtml += `<div style="margin-top: 15px;">${data.html.replace(/\n/g, "")}</div>`;
                                }
                                resultContent.innerHTML = resultHtml;
                            } else {
                                showError(
                                    "查询失败: " + (data.message || "未知错误"),
                                );
                            }
                        } catch (error) {
                            // 隐藏加载状态
                            loader.style.display = "none";
                            submitBtn.disabled = false;
                            showError("发生错误: " + error.message);
                        }
                    });
                }

                function showError(message) {
                    result.style.display = "block";
                    resultContent.innerHTML = `<p class="error">${message}</p>`;
                }
            });
        </script>
    </body>
</html>
