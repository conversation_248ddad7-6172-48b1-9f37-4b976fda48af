<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP 账号查询</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 30px;
            width: 100%;
            max-width: 680px;
            text-align: center;
        }

        .title {
            color: #333;
            font-size: 24px;
            font-weight: normal;
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .input-label {
            display: block;
            color: #333;
            font-weight: normal;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
        }

        .input-field:focus {
            border-color: #666;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: #333;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .submit-btn:hover {
            background: #555;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .result-container {
            display: none;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }

        .result-title {
            color: #333;
            font-size: 16px;
            font-weight: normal;
            margin-bottom: 15px;
            text-align: center;
        }

        .result-item {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .result-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .result-value {
            color: #666;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .success {
            border-left: 3px solid #28a745;
        }

        .error {
            border-left: 3px solid #dc3545;
        }

        .error-message {
            background: #fff2f2;
            color: #d63384;
            padding: 15px;
            border: 1px solid #f8d7da;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .message-error {
            background: #fff2f2;
            color: #d63384;
            padding: 15px;
            border: 1px solid #f8d7da;
            border-radius: 4px;
            border-left: 3px solid #dc3545;
            text-align: center;
        }

        .tips-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .tips-title {
            color: #495057;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tips-title::before {
            content: "💡";
            font-size: 18px;
        }

        .tips-image {
            max-width: 360px;
            height: auto;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tips-description {
            color: #6c757d;
            font-size: 14px;
            margin-top: 12px;
            line-height: 1.5;
        }

        .download-link {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }

        .download-link:hover {
            background: #0056b3;
            text-decoration: none;
            color: white;
        }

        .download-link::before {
            content: "⬇️";
            margin-right: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">VIP账号用量查询</h1>

        

        <form id="vipForm">
            <div class="input-group">
                <label class="input-label" for="tokenInput">请输入权限码：</label>
                <input 
                    type="text" 
                    id="tokenInput" 
                    class="input-field" 
                    placeholder="请输入您的权限码"
                    required
                >
            </div>
            
            <button type="submit" class="submit-btn" id="submitBtn">
                验证权限
            </button>
        </form>

        <div class="loading" id="loading">
            正在验证权限，请稍候...
        </div>

        <div class="result-container" id="resultContainer">
            <div class="result-title">验证结果</div>
            <div id="resultContent"></div>
        </div>
        <!-- Tips 教程区域 -->
        <div class="tips-section">
            <div class="tips-title">使用教程</div>
            <img src="https://img.1953615.xyz/m1/2025-07-27-06-46-OZGAzq.png" alt="VIP账号提取使用教程" class="tips-image">
            <div class="tips-description">
                        先 Sign out，然后卸载原官方 Augment Code 插件
                    </div>
            <a target="_blank" href="https://767700.xyz/download/vscode-augment-0.516.xiaoqingwa.vsix" class="download-link">
                Augment Code 一键登录插件下载，（拖入VSCode/Cursor插件区域后重启）</a>
        </div>
    </div>

    

    <script>
        document.getElementById('vipForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('tokenInput').value.trim();
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const resultContainer = document.getElementById('resultContainer');
            const resultContent = document.getElementById('resultContent');
            
            if (!token) {
                alert('请输入权限码');
                return;
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            loading.style.display = 'block';
            resultContainer.style.display = 'none';
            
            try {
                const response = await fetch(`https://account.767700.xyz/?token=${encodeURIComponent(token)}`);
                const data = await response.json();

                // 隐藏加载状态
                loading.style.display = 'none';
                submitBtn.disabled = false;

                // 显示结果
                resultContainer.style.display = 'block';

                let resultHtml = '';

                // 检查是否有message字段，如果有则表示权限码错误
                if (data.message !== undefined) {
                    resultHtml = `
                        <div class="message-error">
                            <div class="result-label">权限码错误:</div>
                            <div class="result-value">${data.message}</div>
                        </div>
                    `;
                } else {
                    // 显示success字段
                    // resultHtml += `
                    //     <div class="result-item ${data.success ? 'success' : 'error'}">
                    //         <div class="result-label">验证状态:</div>
                    //         <div class="result-value">${data.success ? '验证成功' : '验证失败'}</div>
                    //     </div>
                    // `;

                    // // 显示token字段
                    // if (data.token !== undefined) {
                    //     resultHtml += `
                    //         <div class="result-item">
                    //             <div class="result-label">重置权限码/VIP 权限码:</div>
                    //             <div class="result-value">${data.token}</div>
                    //         </div>
                    //     `;
                    // }

                    // 显示result字段
                    if (data.time !== undefined) {
                        resultHtml += `
                            <div class="result-item">
                                <div class="result-label">账号创建时间:</div>
                                <div>
                                    ${data.time === null ? 'null' : data.time}
                                    ${(() => {
                                        if (data.time) {
                                            const today = new Date();
                                            const expireDate = new Date(data.time);
                                            expireDate.setMonth(expireDate.getMonth() + 1);
                                            if (today.toISOString().split('T')[0] === expireDate.toISOString().split('T')[0]) {
                                                return '<span style="color:red;margin-left:8px;">⚠️ 账号已过期</span>';
                                            }
                                        }
                                        return '';
                                    })()}
                                </div>
                            </div>
                        `;
                    }

                    // 显示account字段
                    if (data.account !== undefined) {
                        resultHtml += `
                            <div class="result-item">
                                <div class="result-label">账户信息（可在其他 IDE 登录）:</div>
                                <div class="result-value">${data.account}</div>
                            </div>
                        `;
                    }

                    // 处理useage字段，获取用量信息
                    if (data.useage !== undefined && data.useage !== null && data.useage !== '') {
                        let useageArray = [];

                        // 如果useage是字符串，尝试解析为JSON数组
                        if (typeof data.useage === 'string') {
                            try {
                                useageArray = JSON.parse(data.useage);
                            } catch (e) {
                                console.error('解析useage字符串失败:', e);
                                useageArray = [];
                            }
                        } else if (Array.isArray(data.useage)) {
                            useageArray = data.useage;
                        }

                        // 检查解析后的数组是否有效
                        if (Array.isArray(useageArray) && useageArray.length > 0) {
                            resultHtml += `
                                <div class="result-item">
                                    <div class="result-label">用量信息（切换新账号开始计算）:</div>
                                    <div class="result-value" id="usageInfo">
                                        <div style="display:flex;align-items:center;">
                                            <div style="
                                                width: 16px;
                                                height: 16px;
                                                border: 2px solid #e0e0e0;
                                                border-top-color: #1890ff;
                                                border-radius: 50%;
                                                animation: spin 1s linear infinite;
                                                margin-right: 8px;
                                            "></div>
                                            <span>正在获取用量信息...</span>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // 异步获取用量信息
                            setTimeout(async () => {
                                await fetchUsageInfo(useageArray);
                            }, 100);
                        }
                    }

                    // 显示其他可能的字段
                    // Object.keys(data).forEach(key => {
                    //     if (!['success', 'token', 'result', 'account', 'message'].includes(key)) {
                    //         resultHtml += `
                    //             <div class="result-item">
                    //                 <div class="result-label">${key}:</div>
                    //                 <div class="result-value">${typeof data[key] === 'object' ? JSON.stringify(data[key], null, 2) : data[key]}</div>
                    //             </div>
                    //         `;
                    //     }
                    // });
                }

                resultContent.innerHTML = resultHtml;
                
            } catch (error) {
                // 隐藏加载状态
                loading.style.display = 'none';
                submitBtn.disabled = false;
                
                // 显示错误信息
                resultContainer.style.display = 'block';
                resultContent.innerHTML = `
                    <div class="error-message">
                        <strong>请求失败:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        });

        // 获取用量信息的函数
        async function fetchUsageInfo(useageArray) {
            const usageInfoElement = document.getElementById('usageInfo');
            let usageHtml = '';

            // 计算总额度
            const totalCredits = useageArray.length * 650;
            let totalBalance = 0;
            let totalUsed = 0;
            let validTokenCount = 0;

            try {
                // 隐藏总额度信息
                // usageHtml += `<div style="margin-bottom: 12px; padding: 10px; background: #e3f2fd; border-radius: 4px; border-left: 4px solid #2196f3;">`;
                // usageHtml += `<strong>实时总额度（会定期补充）:</strong> ${totalCredits}`;
                // usageHtml += `</div>`;

                for (let i = 0; i < useageArray.length; i++) {
                    const token = useageArray[i].trim();
                    if (token) {
                        try {
                            const response = await fetch(`https://checkvip.767700.xyz?token=${encodeURIComponent(token)}`);
                            const usageData = await response.json();

                            // 获取credits_balance字段
                            let creditsBalance = 0;
                            let usedCredits = 0;

                            if (usageData && usageData.data && (typeof usageData.data.credits_balance === 'number' || typeof usageData.data.credits_balance === 'string')) {
                                // 将 credits_balance 转换为数字，并处理浮点数精度问题
                                creditsBalance = parseFloat(usageData.data.credits_balance);
                                usedCredits = Math.round(650 - creditsBalance);

                                totalBalance += creditsBalance;
                                totalUsed += usedCredits;
                                validTokenCount++;

                                // 显示每个token的详细信息（用于调试）
                                // usageHtml += `<div style="margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">`;
                                // usageHtml += `Token ${i + 1}: 剩余 ${creditsBalance} | 已使用 ${usedCredits}`;
                                // usageHtml += `</div>`;
                            } else {
                                usageHtml += `<div style="margin-bottom: 8px; padding: 8px; background: #fff2f2; border-radius: 4px; color: #d63384; font-size: 12px;">`;
                                usageHtml += `Token ${i + 1}: 无法获取额度信息`;
                                usageHtml += `</div>`;
                            }
                        } catch (error) {
                            usageHtml += `<div style="margin-bottom: 8px; padding: 8px; background: #fff2f2; border-radius: 4px; color: #d63384; font-size: 12px;">`;
                            usageHtml += `Token ${i + 1}: ${error.message}`;
                            usageHtml += `</div>`;
                        }
                    }
                }

                // 计算剩余总额度 = 实时总额度 - 已使用额度
                const remainingCredits = totalCredits - Math.round(totalUsed);

                // 添加汇总信息 - 只显示已使用额度和使用率
                usageHtml += `<div style="margin-top: 12px; padding: 10px; background: #f3e5f5; border-radius: 4px; border-left: 4px solid #9c27b0;">`;
                usageHtml += `<div style="margin-bottom: 8px;"><strong>已使用额度:</strong> ${Math.round(totalUsed)}</div>`;
                // usageHtml += `<div><strong>使用率:</strong> ${totalCredits > 0 ? ((totalUsed / totalCredits) * 100).toFixed(2) : 0}%</div>`;
                usageHtml += `</div>`;

                if (usageHtml) {
                    usageInfoElement.innerHTML = usageHtml;
                } else {
                    usageInfoElement.innerHTML = '没有有效的token';
                }
            } catch (error) {
                usageInfoElement.innerHTML = `获取用量信息失败: ${error.message}`;
            }
        }

        // 回车键提交
        document.getElementById('tokenInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('vipForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>

