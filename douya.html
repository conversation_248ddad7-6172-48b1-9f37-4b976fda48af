<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发送消息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        form {
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: auto;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        textarea {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            
            border-radius: 4px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        textarea{min-height: 300px;}

        button {
            background-color: #5cb85c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }

        button:hover {
            background-color: #4cae4c;
        }
    </style>
</head>

<body>
    <h1>发送消息</h1>
    <form id="messageForm">
        <label for="title">标题:</label>
        <input type="text" id="title" name="title" required><br><br>

        <label for="content">内容:</label>
        <textarea id="content" name="content" required></textarea><br><br>

        <label for="torooms">群:</label>
        <input type="text" id="torooms" name="torooms" required><br><br>

        <button type="submit">发送</button>
    </form>

    <script>
        document.getElementById('messageForm').addEventListener('submit', function (event) {
            event.preventDefault();

            var myHeaders = new Headers();
            myHeaders.append("Authorization", "Bearer secret_NWTLrmjDWuZRMPwAeTUXZpwDfgHaxzpB");
            myHeaders.append("Content-Type", "application/json");

            var raw = JSON.stringify({
                "msg": document.getElementById('content').value || "测试",
                "title": document.getElementById('title').value || "测试",
                "torooms": document.getElementById('torooms').value || "room_AsyDp4a70b9"
            });

            var requestOptions = {
                method: 'POST',
                headers: {
                    "Authorization": "Bearer secret_NWTLrmjDWuZRMPwAeTUXZpwDfgHaxzpB",
                    "Content-Type": "application/json"
                },
                body: raw,
                mode: 'no-cors'

            };

            fetch("https://douyahook.12050231.xyz", requestOptions)
                .then(response => response.json())
                .then(result => console.log(result))
                .catch(error => console.log('error', error));
        });
    </script>
</body>

</html>
