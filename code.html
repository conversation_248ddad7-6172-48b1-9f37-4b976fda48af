
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <link rel="icon" href="https://www.ppanda.com/images/logo-frog-all.png" type="image/x-icon">
    <title>邮箱验证码临时查询</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --error-color: #dc3545;
            --success-color: #28a745;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            padding: 20px;
            /* 防止移动端缩放和滚动问题 */
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            text-size-adjust: 100%;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow-x: hidden;
        }

        .container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 750px;
            padding: 35px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .container:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
            transform: translateY(-2px);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 28px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: white;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            /* 恢复输入框的用户交互 */
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        input[type="email"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.2);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3a56b8;
        }

        button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }

        .result {
            margin-top: 25px;
            padding: 15px;
            border-radius: 6px;
            background-color: var(--secondary-color);
            display: none;
            /* 允许选择结果文本 */
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        .result.show {
            display: block;
        }

        .result h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .result p {
            margin-bottom: 5px;
        }

        .error {
            color: var(--error-color);
        }

        .success {
            color: var(--success-color);
        }

        .loader {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loader.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 20px 15px;
                margin: 10px 0;
            }

            h1 {
                font-size: 24px;
                margin-bottom: 20px;
            }

            input[type="email"] {
                font-size: 16px; /* 防止iOS缩放 */
                padding: 15px;
            }

            button {
                padding: 15px 20px;
                font-size: 16px;
            }

            .loader p {
                font-size: 14px;
                line-height: 1.4;
            }
        }
        .tips {
            margin-top: 20px;
            text-align: center;

        }

        .tips img {
            width: 300px;
            margin-top: 20px;
            border-radius: 10px;
            max-width: 100%;
            height: auto;
        }

        /* 确保所有图片在移动端响应式 */
        img {
            max-width: 100%;
            height: auto;
        }

        /* 防止长链接和文本超出屏幕 */
        a {
            word-break: break-all;
            overflow-wrap: break-word;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-x: auto;
            max-width: 100%;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1><img src="https://www.ppanda.com/images/logo-frog-all.png" width="30px" alt=""> 验证码接码查询</h1>
        <div style="text-align: center;margin: 10px;color: red;font-size: 14px;">登录新账号前，记得先 Sign Out 旧账号。需要开魔法哦。</div>
        <div style="text-align: center;margin: 10px;color: red;font-size: 14px;">官网登录确认 OK 即可，其他都是本地配置/网络问题。需自行排查</div>
        <!-- <div style="text-align: center;margin: 10px;color: red;font-size: 14px;">安装方案一重置插件，搭配 accessToken 一键登录可解决账号被封问题</div> -->
        <form id="emailForm">
            <div class="form-group">
                <label for="email">请输入邮箱地址：</label>
                <input type="email" id="email" name="email" placeholder="例如: <EMAIL>" required>
            </div>

            <button type="submit" id="submitBtn">查询</button>
        </form>

        <div class="loader" id="loader">
            <div class="spinner"></div>
            <p>正在查询中，链路较长，繁忙时请耐心等待... <b style="color: #4a6bdf;">尽量不要多次发送</b></p>
        </div>

        <div class="result" id="result">
            <h3>查询结果</h3>
            <div class="tips"><img src="https://img.262700.xyz/images/2025-06-19-Oj1sOlEpTZqu.png" alt=""></div>
            <div id="resultContent"></div>
            
        </div>
    </div>
    <div class="tips-item" style="margin-top: 20px;">
        <h3><a style="color: #f6460c;" href="https://mp.weixin.qq.com/s/fVUCvFJ88Vdb4maHRLXdvQ?scene=1&click_id=10v" target="_blank">Augment 登录失败(Sign in failed.
        )解决方案</a></h3>
    </div>

    <div class="tips-item solution" style="margin-top: 20px;">
        <h3>Augment Code 退出</h3>
        <ol>
            <li>Ctrl+Shift+P 打开命令面板</li>
            <li>输入：<code>Augment Sign Out</code></li>
            <li>找到 Sign Out 退出</li>
        </ol>
    </div>

    <div class="tips-container">
        <div class="tips-card">
            <h2>换号插件有需要可以下载，可搭配<a style="color: #4a6bdf;"  href="pool.html" target="_blank">免费号池</a>使用</h2>
            <div class="tips-content">

                <div class="tips-item warning">
                    <div>
                        <!-- <div>⚠️ 因 Augment Code 风控升级，多次使用账号会出现
                        <p><code>Your access has been restricted. To continue, you may upgrade to a paid subscription.</code></p>
                        </div> -->
                        <div class="solution-highlight">
                            <div class="solution-badge">重置/登录插件</div>
                            <h4 class="solution-title">🟢 【VSCode/Cursor/CodeBuddy/Kiro/Trae 都支持】</h4>
                            <ol class="solution-steps">
                                <li class="step-item">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <strong>退出并卸载</strong><br>
                                        先 Sign out，然后卸载原官方 Augment Code 插件
                                        <img src="https://img.1953615.xyz/m1/2025-07-24-21-19-vvgunK.png" alt="">
                                    </div>
                                </li>
                                <li class="step-item">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <strong>下载重置/登录插件</strong><br>
                                        <a href="https://767700.xyz/download/vscode-augment-0.516.xiaoqingwa.vsix" class="download-link">内置重置/登录 Augment 插件 0.516</a>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <span class="step-number">3</span>
                                    <div class="step-content">
                                        <strong>安装插件</strong><br>
                                        安装载重置/登录插件，（<span style="color: #f3e016;">把 vsix 拖入插件区域</span></span>）后重启 VSCode
                                    </div>
                                </li>
                                <li class="step-item">
                                    <span class="step-number">4</span>
                                    <div class="step-content">
                                        <strong>重置并登录</strong><br>
                                        最后点击 Sign in，然后选择你需要的操作。（插件自动更新记得关）
                                        <img width="600px" src="https://img.1953615.xyz/m1/2025-07-25-21-43-awSsCM.png" alt="" class="step-image">
                                    </div>
                                </li>
                                <li class="step-item">
                                    <span class="step-number">5</span>
                                    <div class="step-content">
                                        <strong>选择选项 5，填入选项 4 获取的 tenantURL 和 accessToken 一键登录，沉浸式换号更方便</strong>
                                        <img src="https://img.1953615.xyz/m1/2025-07-26-20-36-2025-07-2612.gif" width="600px" alt="">
                                    </div>
                                </li>
                                <li class="step-item">
                                    <span class="step-number">6</span>
                                    <div class="step-content">
                                        <strong>有需要的也可以加客服微信选择 VIP 包月模式，一键切换更省心（一月不低于 4500 条）</strong>
                                        <img src="https://img.1953615.xyz/m1/2025-07-26-20-25-2025-07-261.gif" width="600px" alt="">
                                    </div>
                                </li>
                            </ol>
                        </div>

                        
                        <!-- <div class="collapsible-section">
                            <div class="collapsible-header" onclick="toggleCollapse(this)">
                                <p>🟢 方案二（JetBrains 建议版本 2025.1.1 以上）(不推荐)</p>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="collapsible-content">
                                <ol>
                                    <li><a href="###" onclick="alert('拍下后发最新链接')">下载 Augment 管理插件</a></li>
                                    <li>安装 Augment 管理插件，后重启 IDEA <img width="200px" src="img/SCR-20250527-pzpe.png" alt=""><img width="600px" src="img/SCR-20250527-pzte.png" alt="">
                                    <li>每次使用新的账号先生成一个新的SessionID，换IP换号重启，再登录自己新搞的账号即可<img width="600px" src="img/SCR-20250527-pwxi-2.png" alt="">
                                </ol>
                            </div>
                        </div> -->

                        <div class="collapsible-section">
                            <div class="collapsible-header" onclick="toggleCollapse(this)">
                                <p>🟢 方案二：网上收集的重置方案</p>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="collapsible-content show active">
                                <div class="solution-list">

                                    <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>Go Augment Cleaner (JetBrains)</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>下载 <a href="https://github.com/yuaotian/go-augment-cleaner/releases/"
                                                    target="_blank" class="download-link">go-augment-cleaner</a></p>
                                            <div class="usage-guide">
                                                <p class="description">Augment 插件清理工具是一款专业的多功能工具，集成了 Augment 插件数据清理 和 API 域名优化 两大核心功能。通过智能化的交互式菜单和丰富的命令行参数，为用户提供灵活、安全、高效的使用体验。</p>
                                                <h5>🎯 核心功能：</h5>
                                                <ul>
                                                    <li>🧹 彻底清理 Augment 插件的所有跟踪数据</li>
                                                    <li>🚀 API 域名测速 和 hosts 文件优化</li>
                                                    <li>🌐 代理支持 (Clash、V2Ray、自定义代理)</li>
                                                    <li>🎛️ 交互式主菜单 和命令行双模式</li>
                                                    <li>🔒 安全模式 和干运行预览</li>
                                                    <li>📊 详细报告 和进度显示</li>
                                                    <li>🌍 跨平台兼容 (Windows、macOS、Linux)</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>Augmentcode修复版.exe</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>下载 <a href="https://img.262700.xyz/images/2025-06-25-fVVuL5Augmentcode修复版.exe"
                                                    target="_blank" class="download-link">Augmentcode修复版.exe</a></p>
                                            <div class="usage-guide">
                                                <img src="https://img.262700.xyz/images/2025-06-25-z2ISP4zu59F1.png" alt="">
                                                <p>据说目前无须输入卡密即可使用，程序使用有问题可能是没更新！请先更新</p>
                                            </div>
                                        </div>
                                    </div> -->

                                    <!-- <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>acaugment-assistant-1.0.0 JetBrains</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>下载 <a href="https://img.262700.xyz/images/2025-06-24-YtdOOfaugment-assistant-1.0.0.jar"
                                                    target="_blank" class="download-link">augment-assistant-1.0.0.jar</a></p>
                                            <div class="usage-guide">
                                                <h5>操作步骤：</h5>
                                                <a href="https://img.262700.xyz/images/2025-06-24-rQHBacaugment-assistant-1.0.0.pdf" target="_blank"
                                                    class="download-link">augment-assistant-1.0.0.pdf</a>
                                            </div>
                                        </div>
                                    </div> -->

                                    <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>JetBrains Augment Cleaner</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>访问 <a href="https://plugins.caicode.org/jetbrains-augment-cleaner/" target="_blank"
                                                    class="download-link">JetBrains Augment Cleaner</a></p>
                                            <p class="description">用于清理和备份 JetBrains IDE 中 Augment 插件数据的跨平台桌面应用程序，支持自动发现、清理环境、备份还原等功能。作者填 crazy</p>
                                        </div>
                                    </div>
                                    <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>augment-free</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>访问 <a href="https://github.com/xn030523/augment-free/releases" target="_blank"
                                                    class="download-link">JetBrains Augment Cleaner</a></p>
                                            <p class="description">一键解锁 VS Code Augment 功能的免费工具</p>
                                        </div>
                                    </div>
                                    <!-- <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>augment-vip</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>访问 <a href="https://github.com/Ran-Mewo/augment-vip/releases" target="_blank"
                                                    class="download-link">augment-vip</a></p>
                                            <p class="description">Remove augmentcode free trial account limit. Supports all OSes and all IDEs (IntelliJ,
                                                VSCode, all Jetbrains IDEs, all
                                                VSCode forks like Cursor, etc!)</p>
                                        </div>
                                    </div> -->
                                    <div class="solution-item">
                                        <div class="solution-title">
                                            <span class="solution-number">⭐</span>
                                            <h4>YAugment</h4>
                                        </div>
                                        <div class="solution-content">
                                            <p>访问 <a href="https://docs.qq.com/aio/p/sckmhgp8sr4gzcz?p=PwQmmASGlq14f2PmhloHR8" target="_blank"
                                                    class="download-link">YAugment</a></p>
                                            <p class="description">用于清理 Augment 插件数据的跨平台桌面应用程序。</p>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <style>
                            .collapsible-section {
                                margin: 20px 0;
                                border: 1px solid #e0e0e0;
                                border-radius: 8px;
                                overflow: hidden;
                            }

                            .collapsible-header {
                                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                padding: 15px 20px;
                                cursor: pointer;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                transition: background-color 0.3s ease;
                            }

                            .collapsible-header:hover {
                                background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
                            }

                            .collapsible-header p {
                                margin: 0;
                                font-weight: 600;
                                color: #495057;
                                font-size: 16px;
                            }

                            .arrow {
                                font-size: 14px;
                                color: #6c757d;
                                transition: transform 0.3s ease;
                            }

                            .collapsible-content {
                                padding: 20px;
                                background-color: #fff;
                            }

                            .solution-list {
                                display: flex;
                                flex-direction: column;
                                gap: 20px;
                            }

                            .solution-item {
                                border: 1px solid #e9ecef;
                                border-radius: 8px;
                                padding: 20px;
                                background-color: #f8f9fa;
                                transition: box-shadow 0.3s ease;
                            }

                            .solution-item:hover {
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                            }

                            .solution-title {
                                display: flex;
                                align-items: center;
                                gap: 12px;
                                margin-bottom: 15px;
                            }

                            .solution-number {
                                background-color: #4a6bdf;
                                color: white;
                                width: 28px;
                                height: 28px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-weight: bold;
                                font-size: 14px;
                            }

                            .solution-title h4 {
                                margin: 0;
                                color: #343a40;
                                font-size: 18px;
                            }

                            .solution-content {
                                margin-left: 40px;
                            }

                            .download-link {
                                display: inline-block;
                                background: #ffffff;
                                color: #1d4ed8 !important;
                                text-decoration: none !important;
                                font-weight: 600;
                                padding: 8px 16px;
                                border-radius: 6px;
                                border: 2px solid #1d4ed8;
                                box-shadow: 0 2px 6px rgba(29, 78, 216, 0.15);
                                transition: all 0.3s ease;
                                margin: 3px 0;
                                font-size: 13px;
                            }

                            .download-link::before {
                                content: "⬇️";
                                margin-right: 6px;
                                font-size: 14px;
                            }

                            .download-link:hover {
                                background: #f8fafc;
                                color: #1e3a8a !important;
                                border-color: #1e3a8a;
                                transform: translateY(-1px);
                                box-shadow: 0 3px 10px rgba(30, 58, 138, 0.2);
                            }

                            .usage-steps,
                            .usage-guide {
                                margin-top: 15px;
                                padding: 15px;
                                background-color: white;
                                border-radius: 6px;
                                border-left: 4px solid #4a6bdf;
                            }

                            .usage-steps ol,
                            .usage-guide ol,
                            .usage-guide ul {
                                margin: 10px 0;
                                padding-left: 20px;
                            }

                            .usage-steps li,
                            .usage-guide li {
                                margin-bottom: 8px;
                                line-height: 1.6;
                            }

                            .usage-guide h5 {
                                margin: 15px 0 8px 0;
                                color: #495057;
                                font-size: 14px;
                                font-weight: 600;
                            }

                            .warning-text {
                                color: #dc3545;
                                font-size: 14px;
                                margin-top: 10px;
                                padding: 8px 12px;
                                background-color: #fff5f5;
                                border-radius: 4px;
                                border-left: 3px solid #dc3545;
                            }

                            .description {
                                color: #6c757d;
                                font-style: italic;
                                margin-top: 10px;
                                line-height: 1.6;
                            }

                            .solution-image {
                                margin-top: 15px;
                                text-align: center;
                            }

                            .solution-image img {
                                max-width: 100%;
                                height: auto;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            }

                            @media (max-width: 768px) {
                                .collapsible-header {
                                    padding: 12px 15px;
                                }

                                .collapsible-content {
                                    padding: 15px;
                                }

                                .solution-item {
                                    padding: 15px;
                                }

                                .solution-content {
                                    margin-left: 0;
                                }

                                .solution-title {
                                    flex-direction: column;
                                    align-items: flex-start;
                                    gap: 8px;
                                }

                                .solution-title h4 {
                                    font-size: 16px;
                                }
                            }
                        </style>
                    </div>
                </div>
                <!-- <div class="tips-item warning">
                    <div>
                        <p>⚠️ 登录需要梯子，<span style="color: red;">尽量不要使用便宜的梯子，节点 IP 质量差会增加风控概率</span>，此类问题不售后，可以访问 <a target="_blank" href="https://ping0.cc/">查询 IP 风控</a>
                        </p>
                    </div>
                </div> -->
                

                <div class="tips"><img src="https://img.262700.xyz/images/2025-07-31-MuqELx8O0rrL.png" alt=""></div>
            </div>
        </div>
    </div>


        <style>
        .tips-container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
            width: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .tips-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .collapsible-section {
            margin-top: 15px;
        }

        .collapsible-header {
            cursor: pointer;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
            user-select: none;
        }

        .collapsible-header:hover {
            background-color: #e9ecef;
        }

        .collapsible-header p {
            margin: 0;
            font-weight: 500;
        }

        .arrow {
            transition: transform 0.3s ease;
            font-size: 14px;
            color: #666;
        }

        .collapsible-header.active .arrow {
            transform: rotate(180deg);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            padding: 0 10px;
        }

        .collapsible-content.show {
            max-height: 5500px;
            padding: 15px 10px;
        }

        /* 方案一重点突出样式 */
        .solution-highlight {
            background: #4a6bdf;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 4px 15px rgba(74, 107, 223, 0.2);
            position: relative;
            overflow: hidden;
        }

        .solution-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .solution-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 20px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .solution-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .step-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .step-item:last-child {
            margin-bottom: 0;
        }

        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.9);
            color: #4a6bdf;
            border-radius: 50%;
            font-weight: bold;
            font-size: 16px;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .step-content {
            flex: 1;
            line-height: 1.6;
        }

        .step-content strong {
            color: #fff;
            font-size: 16px;
            display: block;
            margin-bottom: 5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .step-image {
            margin-top: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 100%;
            height: auto;
        }

        .download-link {
            display: inline-block;
            background: #ffffff;
            color: #2563eb !important;
            text-decoration: none !important;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 8px;
            border: 2px solid #2563eb;
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
            transition: all 0.3s ease;
            font-size: 14px;
            margin: 5px 0;
            position: relative;
        }

        .download-link::before {
            content: "⬇️";
            margin-right: 8px;
            font-size: 16px;
        }

        .download-link:hover {
            background: #f1f5f9;
            color: #1e40af !important;
            border-color: #1e40af;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
        }

        .download-link:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
        }

        /* 方案一移动端适配 */
        @media (max-width: 768px) {
            .solution-highlight {
                padding: 20px 15px;
                margin: 15px 0;
            }

            .solution-title {
                font-size: 16px;
                margin-bottom: 15px;
            }

            .solution-badge {
                font-size: 11px;
                padding: 5px 12px;
                margin-bottom: 12px;
            }

            .step-item {
                padding: 12px;
                margin-bottom: 15px;
                flex-direction: column;
                align-items: flex-start;
            }

            .step-number {
                width: 30px;
                height: 30px;
                font-size: 14px;
                margin-right: 0;
                margin-bottom: 10px;
                align-self: flex-start;
            }

            .step-content {
                width: 100%;
            }

            .step-content strong {
                font-size: 15px;
            }

            .step-image {
                width: 100%;
                max-width: 100%;
                height: auto;
                margin-top: 8px;
            }
        }

        .tips-card h2 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .tips-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .tips-item {
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .tips-item.warning {
            background-color: #fff8e6;
            border-left: 4px solid #ffcc00;
        }

        .tips-item.solution {
            background-color: #f0f7ff;
            border-left: 4px solid #1890ff;
        }

        .tips-item.alternative {
            background-color: #f6f6f6;
            border-left: 4px solid #666;
        }

        .tips-item.token {
            background-color: #f6f6f6;
            border-left: 4px solid #e67fea;
        }

        .tips-icon {
            font-size: 20px;
            font-style: normal;
        }

        .tips-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }

        .tips-item p {
            margin: 0 0 10px 0;
            line-height: 1.6;
            /* 允许选择提示文本 */
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        .tips-item ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .tips-item li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .tips-item code {
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .tips-container {
                padding: 0 5px;
                margin: 20px 0;
                max-width: 100%;
                width: 100vw;
                box-sizing: border-box;
            }

            .tips-card {
                padding: 10px;
                margin: 0 5px 15px 5px;
                width: calc(100% - 10px);
                box-sizing: border-box;
                overflow-x: hidden;
            }

            .tips-card h2 {
                font-size: 20px;
                margin-bottom: 15px;
            }

            .tips-item {
                flex-direction: column;
                gap: 10px;
                padding: 8px;
                width: 100%;
                box-sizing: border-box;
                overflow-x: hidden;
                word-wrap: break-word;
                word-break: break-word;
            }

            .tips-item h3 {
                font-size: 16px;
            }

            .tips-item p {
                font-size: 14px;
                line-height: 1.5;
            }

            .tips-item ol {
                padding-left: 0;
            }

            .tips-item li {
                font-size: 14px;
                margin-bottom: 6px;
            }

            .tips-item code {
                font-size: 12px;
                word-break: break-all;
                overflow-wrap: break-word;
                white-space: pre-wrap;
                max-width: 100%;
                display: inline-block;
                box-sizing: border-box;
            }

            .collapsible-header {
                padding: 8px;
            }

            .collapsible-header p {
                font-size: 14px;
            }

            .collapsible-content {
                padding: 0 5px;
                width: 100%;
                box-sizing: border-box;
                overflow-x: hidden;
            }

            .collapsible-content.show {
                padding: 8px 5px;
            }

            .tips-icon {
                align-self: flex-start;
            }
        }
    </style>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('emailForm');
            const loader = document.getElementById('loader');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const submitBtn = document.getElementById('submitBtn');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value.trim();

                if (!email) {
                    showError('请输入有效的邮箱地址');
                    return;
                }

                // 显示加载状态
                loader.classList.add('show');
                result.classList.remove('show');
                submitBtn.disabled = true;

                try {
                    // 获取URL中的token参数
                    const urlParams = new URLSearchParams(window.location.search);
                    const token = urlParams.get('token');
                    
                    // 构建API URL，如果有token则添加到参数中
                    let apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(email)}`;
                    if (token) {
                        apiUrl += `&token=${encodeURIComponent(token)}`;
                    }
                    
                    const response = await fetch(apiUrl);

                    if (!response.ok) {
                        throw new Error('未查询到相关邮件，请确认邮箱是否正确，稍后再重新查询');
                    }

                    const data = await response.json();

                    data.date = new Date(data.date).toLocaleString('zh-CN', {
                        timeZone: 'Asia/Shanghai',
                        hour12: false,  // 24小时制
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    })

                    // 隐藏加载状态
                    loader.classList.remove('show');
                    result.classList.add('show');
                    submitBtn.disabled = false;

                    if (data.result) {
                        let resultHtml = ``;

                        if (data.subject) {
                            resultHtml += `
                <h2>${data.subject}</h2>
                <div class="meta">发件人：${data.from} | 收件人：${data.to} | 时间：${data.date}</div>
            `;
                        }



                        if (data.html) {
                            resultHtml += `<p>${data.html.replace(/\n/g, '')}</p>`;
                        }
                        resultContent.innerHTML = resultHtml;
                        try {
                            // await navigator.clipboard.writeText(data.verificationCode);
                        } catch (error) {

                        }

                    } else {
                        showError('查询失败: ' + (data.message || '未知错误'));
                    }
                } catch (error) {
                    // 隐藏加载状态
                    loader.classList.remove('show');
                    submitBtn.disabled = false;

                    showError('发生错误: ' + error.message);
                }
            });

            function showError(message) {
                result.classList.add('show');
                resultContent.innerHTML = `<p class="error">${message}</p>`;
            }
        });

        // 折叠面板功能
        function toggleCollapse(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('.arrow');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                header.classList.remove('active');
            } else {
                content.classList.add('show');
                header.classList.add('active');
            }
        }
    </script>
</body>
</html>
