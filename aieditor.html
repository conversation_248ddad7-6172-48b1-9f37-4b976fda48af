<!doctype html>
<html lang="en">

<head>
    <title>AI 编辑器</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link type="text/css" rel="stylesheet" href="./aieditor/style.css">
    <script type="module">
        import { AiEditor } from './aieditor/index.js'
        new AiEditor({
            element: "#aiEditor",
            placeholder: "点击输入内容...",
            content: '这是一个面向 AI 的下一代富文本编辑器。在行末按空格后再按 / 键可以唤起 AI 功能。',
            ai: {
                models: {
                    openai: {
                        endpoint: "https://api.siliconflow.cn",
                        model: "Qwen/Qwen2.5-32B-Instruct",
                        apiKey: "sk-qctkbhhtznnaskkvfzenkyzwmhtvxjcvqwfgfginzinytfvq"
                    },
                    commands: [
                        {
                            name: "AI 续写-光标前内容",
                            prompt: "请帮我继续扩展一些这段话的内容",
                            text: "focusBefore"
                        }
                    ],
                    menus: [
                        {
                            icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M4 18.9997H20V13.9997H22V19.9997C22 20.552 21.5523 20.9997 21 20.9997H3C2.44772 20.9997 2 20.552 2 19.9997V13.9997H4V18.9997ZM16.1716 6.9997L12.2218 3.04996L13.636 1.63574L20 7.9997L13.636 14.3637L12.2218 12.9495L16.1716 8.9997H5V6.9997H16.1716Z"></path></svg>`,
                            name: "AI 续写1",
                            prompt: "请帮我继续扩展一些这段话的内容",
                            text: "focusBefore",
                            model: "auto",
                        },
                        {
                            icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M15 5.25C16.7949 5.25 18.25 3.79493 18.25 2H19.75C19.75 3.79493 21.2051 5.25 23 5.25V6.75C21.2051 6.75 19.75 8.20507 19.75 10H18.25C18.25 8.20507 16.7949 6.75 15 6.75V5.25ZM4 7C4 5.89543 4.89543 5 6 5H13V3H6C3.79086 3 2 4.79086 2 7V17C2 19.2091 3.79086 21 6 21H18C20.2091 21 22 19.2091 22 17V12H20V17C20 18.1046 19.1046 19 18 19H6C4.89543 19 4 18.1046 4 17V7Z"></path></svg>`,
                            name: "AI 优化2",
                            prompt: "请帮我优化一下这段文字的内容，并返回结果",
                            text: "selected",
                            model: "auto",
                        },
                    ]
                }
            },
        })
    </script>
</head>

<body>
    <div id="aiEditor" style="height: 550px;  margin: 0 auto;width: 1200px;"></div>

</body>

</html>