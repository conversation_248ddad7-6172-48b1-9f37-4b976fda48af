<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>ro 下载 - The AI IDE for prototype to production</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        /* 验证弹窗样式 */
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .auth-modal {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .auth-modal h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .auth-modal p {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .auth-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            margin-bottom: 20px;
            outline: none;
            transition: all 0.3s ease;
        }

        .auth-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .auth-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .auth-btn:active {
            transform: translateY(0);
        }

        .auth-error {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }

        /* 主要内容样式 */
        .main-content {
            display: none;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
            margin: 20px;
        }

        .logo {
            font-size: 48px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 18px;
            margin-bottom: 40px;
            font-weight: 300;
        }

        .system-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .system-info h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 600;
        }

        .system-details {
            color: #666;
            font-size: 14px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin-bottom: 20px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .download-icon {
            font-size: 20px;
        }

        .all-downloads {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .all-downloads h4 {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .download-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .download-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            text-decoration: none;
            color: #666;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .download-item:hover {
            background: #e9ecef;
            color: #333;
        }

        .download-item .platform {
            font-weight: 500;
        }

        .download-item .size {
            color: #999;
            font-size: 12px;
        }

        .unsupported {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .unsupported h3 {
            color: #856404;
            margin-bottom: 8px;
        }

        .unsupported p {
            color: #856404;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }

            .logo {
                font-size: 36px;
            }

            .download-btn {
                font-size: 16px;
                padding: 14px 28px;
            }

            .auth-modal {
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <!-- 验证弹窗 -->
    <div class="auth-overlay" id="authOverlay">
        <div class="auth-modal">
            <h2>🔐 访问验证</h2>
            <p>请输入访问密码以继续</p>
            <form id="authForm">
                <input type="password" id="passwordInput" class="auth-input" placeholder="dTo3ndAKUUnz" value="dTo3ndAKUUnz" required>
                <button type="submit" class="auth-btn">确认访问</button>
                <div class="auth-error" id="authError">
                    ❌ 密码错误，请重新输入
                </div>
            </form>
        </div>
    </div>
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 20 24" fill="none">
    <path
        d="M3.80081 18.5661C1.32306 24.0572 6.59904 25.434 10.4904 22.2205C11.6339 25.8242 15.926 23.1361 17.4652 20.3445C20.8578 14.1915 19.4877 7.91459 19.1361 6.61988C16.7244 -2.20972 4.67055 -2.21852 2.59581 6.6649C2.11136 8.21946 2.10284 9.98752 1.82846 11.8233C1.69011 12.749 1.59258 13.3398 1.23436 14.3135C1.02841 14.8733 0.745043 15.3704 0.299833 16.2082C-0.391594 17.5095 -0.0998802 20.021 3.46397 18.7186V18.7195L3.80081 18.5661Z"
        fill="white"></path>
    <path
        d="M10.9614 10.4413C9.97202 10.4413 9.82422 9.25893 9.82422 8.55407C9.82422 7.91791 9.93824 7.4124 10.1542 7.09197C10.3441 6.81003 10.6158 6.66699 10.9614 6.66699C11.3071 6.66699 11.6036 6.81228 11.8128 7.09892C12.0511 7.42554 12.177 7.92861 12.177 8.55407C12.177 9.73591 11.7226 10.4413 10.9616 10.4413H10.9614Z"
        fill="black"></path>
    <path
        d="M15.0318 10.4413C14.0423 10.4413 13.8945 9.25893 13.8945 8.55407C13.8945 7.91791 14.0086 7.4124 14.2245 7.09197C14.4144 6.81003 14.6861 6.66699 15.0318 6.66699C15.3774 6.66699 15.6739 6.81228 15.8831 7.09892C16.1214 7.42554 16.2474 7.92861 16.2474 8.55407C16.2474 9.73591 15.793 10.4413 15.0319 10.4413H15.0318Z"
        fill="black"></path>
</svg>
    <!-- 主要内容 -->
    <div class="main-content" id="mainContent">
       

        <div class="container">
            <div class="system-info">
                <h3>检测到的系统信息</h3>
                <div class="system-details">
                    <div>操作系统: <span id="os-name">检测中...</span></div>
                    <div>架构: <span id="os-arch">检测中...</span></div>
                    <div>浏览器: <span id="browser-name">检测中...</span></div>
                </div>
            </div>

            <div id="download-section">
                <!-- 下载内容将通过 JavaScript 动态生成 -->
            </div>

            <div class="all-downloads">
                <h4>所有平台下载</h4>
                <div class="download-list">
                    <a href="https://prod.download.desktop.kiro.dev/releases/202507160015-Kiro-win32-x64.exe"
                        class="download-item" target="_blank">
                        <div>
                            <div class="platform">🪟 Windows x64</div>
                        </div>
                        <div class="size">EXE</div>
                    </a>
                    <a href="https://prod.download.desktop.kiro.dev/releases/202507161958-Kiro-dmg-darwin-arm64.dmg"
                        class="download-item" target="_blank">
                        <div>
                            <div class="platform">🍎 macOS Apple Silicon</div>
                        </div>
                        <div class="size">DMG</div>
                    </a>
                    <a href="https://prod.download.desktop.kiro.dev/releases/202507152349-Kiro-dmg-darwin-x64.dmg"
                        class="download-item" target="_blank">
                        <div>
                            <div class="platform">🍎 macOS Intel</div>
                        </div>
                        <div class="size">DMG</div>
                    </a>
                </div>
            </div>
        </div>
        
    </div>
<img src="https://kiro.dev/images/kiro-wordmark.png" alt="">
    <script>
        // 密码验证功能
        const correctPassword = 'dTo3ndAKUUnz';

        document.getElementById('authForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const passwordInput = document.getElementById('passwordInput');
            const authError = document.getElementById('authError');
            const enteredPassword = passwordInput.value;

            if (enteredPassword === correctPassword) {
                // 密码正确，隐藏弹窗并显示主要内容
                document.getElementById('authOverlay').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';

                // 初始化系统检测和下载功能
                renderDownloadSection();
            } else {
                // 密码错误，显示错误信息
                authError.style.display = 'block';
                passwordInput.value = '';
                passwordInput.focus();

                // 添加震动效果
                passwordInput.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    passwordInput.style.animation = '';
                }, 500);
            }
        });

        // 添加震动动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);

        // 系统检测和下载链接配置
        const downloads = {
            windows: {
                url: 'https://prod.download.desktop.kiro.dev/releases/202507160015-Kiro-win32-x64.exe',
                name: 'Windows x64',
                icon: '🪟'
            },
            'mac-arm': {
                url: 'https://prod.download.desktop.kiro.dev/releases/202507161958-Kiro-dmg-darwin-arm64.dmg',
                name: 'macOS Apple Silicon',
                icon: '🍎'
            },
            'mac-intel': {
                url: 'https://prod.download.desktop.kiro.dev/releases/202507152349-Kiro-dmg-darwin-x64.dmg',
                name: 'macOS Intel',
                icon: '🍎'
            }
        };

        function detectSystem() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            // 更新系统信息显示
            document.getElementById('os-name').textContent = getOSName();
            document.getElementById('os-arch').textContent = getArchitecture();
            document.getElementById('browser-name').textContent = getBrowserName();

            // 检测操作系统和架构
            if (userAgent.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                // 更准确的 Mac 架构检测
                // 检查是否为 Apple Silicon (ARM)
                if (platform.includes('arm') ||
                    navigator.userAgentData?.platform === 'macOS' && navigator.userAgentData?.getHighEntropyValues) {
                    return 'mac-arm';
                }
                // 检查是否明确为 Intel
                else if (platform.includes('intel') || platform.includes('x86')) {
                    return 'mac-intel';
                }
                // 使用更现代的方法检测架构
                else {
                    // 尝试通过 WebGL 检测 GPU 信息来判断架构
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    if (gl) {
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_GL);
                            if (renderer.toLowerCase().includes('apple')) {
                                return 'mac-arm';
                            }
                        }
                    }

                    // 最后的判断：如果是较新的 macOS 版本，默认为 Apple Silicon
                    const macVersion = getMacOSVersion();
                    if (macVersion >= 11) { // macOS Big Sur 及以上更可能是 Apple Silicon
                        return 'mac-arm';
                    } else {
                        return 'mac-intel';
                    }
                }
            }

            return null;
        }

        function getOSName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Windows')) return 'Windows';
            if (userAgent.includes('Mac')) return 'macOS';
            if (userAgent.includes('Linux')) return 'Linux';
            if (userAgent.includes('Android')) return 'Android';
            if (userAgent.includes('iOS')) return 'iOS';
            return '未知系统';
        }

        function getArchitecture() {
            const platform = navigator.platform;
            const userAgent = navigator.userAgent;

            // 更准确的架构检测
            if (platform.includes('ARM') || platform.includes('arm') ||
                userAgent.includes('ARM') || userAgent.includes('arm')) {
                return 'ARM (Apple Silicon)';
            }
            if (platform.includes('Intel') || platform.includes('intel') ||
                platform.includes('x86') || userAgent.includes('Intel')) {
                return 'Intel x64';
            }
            if (platform.includes('64')) return 'x64';
            if (platform.includes('32')) return 'x32';

            // 尝试通过 WebGL 检测
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                    if (debugInfo) {
                        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_GL);
                        if (renderer.toLowerCase().includes('apple')) {
                            return 'ARM (Apple Silicon)';
                        }
                    }
                }
            } catch (e) {
                // 忽略错误
            }

            return '未知架构';
        }

        function getMacOSVersion() {
            const userAgent = navigator.userAgent;
            const match = userAgent.match(/Mac OS X (\d+)[._](\d+)/);
            if (match) {
                return parseInt(match[1]);
            }
            // 新版本的 macOS 可能显示为 "Macintosh; Intel Mac OS X"
            if (userAgent.includes('Mac OS X')) {
                // 如果无法解析版本，假设是较新版本
                return 12;
            }
            return 10;
        }

        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            return '未知浏览器';
        }

        function renderDownloadSection() {
            const downloadSection = document.getElementById('download-section');
            const detectedSystem = detectSystem();

            if (detectedSystem && downloads[detectedSystem]) {
                const download = downloads[detectedSystem];
                downloadSection.innerHTML = `
                    <a href="${download.url}" class="download-btn" target="_blank">
                        <span class="download-icon">${download.icon}</span>
                        <span>下载 Kiro for ${download.name}</span>
                        <span class="download-icon">⬇️</span>
                    </a>
                `;
            } else {
                downloadSection.innerHTML = `
                    <div class="unsupported">
                        <h3>⚠️ 系统不支持</h3>
                        <p>抱歉，当前系统暂不支持 Kiro 应用。请从下方选择合适的版本手动下载。</p>
                    </div>
                `;
            }
        }

        // 添加下载统计（可选）
        document.addEventListener('click', function (e) {
            if (e.target.closest('.download-btn') || e.target.closest('.download-item')) {
                console.log('下载链接被点击:', e.target.href || e.target.closest('a').href);
            }
        });
    </script>
</body>

</html>