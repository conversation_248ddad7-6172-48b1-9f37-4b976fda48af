<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>代码高亮 Demo</title>
    <style>
        /* 在线加载字体 */
        @font-face {
            font-family: 'FontWithASyntaxHighlighter';
            src: url('https://oss.suning.com/cms/staticfilebucket/res/public/FontWithASyntaxHighlighter-Regular.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
        }

        body {
            background: #f4f4f4;
            font-family: sans-serif;
            padding: 20px;
        }

        /* 代码块的样式 */
        code {
            display: block;
            background: #2d2d2d;
            color: #ccc;
            padding: 20px;
            border-radius: 5px;
            overflow: auto;
            font-family: 'FontWithASyntaxHighlighter', monospace;
            font-size: 14px;
            line-height: 1.5;

        }
    </style>
</head>

<body>
    <h1>代码高亮 Demo</h1>
    <p>下面展示了一段使用自定义字体和简单样式进行高亮的代码示例：</p>

    <pre class="code">
    <code id="code">
function sendIM({ title = "", content = "", torooms ="room_GCnmm45d92e"}) {
    let timeStamp = (new Date()).getTime(),
        imCode = 'opurrjCw355',
        isrvCode = 'isrvBZJID56'
    imMac = md5Simple(imCode + '2c58ef6d413695b577738968f2bbb5a0' + timeStamp);

    request({
        url: "http://nim.suning.com/center/open/sendGroupMessage.do?isrvCode=" + isrvCode,
        method: "POST",
        json: true,
        headers: {
            "content-type": "application/json",
            "IM-IService-CODE": imCode,
            "IM-IService-MAC": imMac,
            "IM-IService-TIMESTAMP": timeStamp
        },
        body: {
            "title": title,
            "type": "text",
            "content": content,
            "torooms": torooms,
            "atIds": ""
        }
    }, function (error, response, body) {
        console.log('body ', body)
    });
}


  </code></pre>

    <p>此 demo 中使用的字体文件地址为：
        <a href="https://github.com/hlotvonen/FontWithASyntaxHighlighter/raw/refs/heads/main/FontWithASyntaxHighlighter-Regular.woff2"
            target="_blank">
            FontWithASyntaxHighlighter-Regular.woff2
        </a>
    </p>
    <script>
        // 获取URL中的查询参数
        function getQueryParameter(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // 获取code参数的值
        const codeValue = getQueryParameter('code');

        // 将code参数的值设置为div的innerHTML内容
        const displayCodeElement = document.getElementById('code');
        if (codeValue) {
            displayCodeElement.innerHTML = codeValue;
        }

    </script>
</body>

</html>