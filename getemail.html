<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证码临时查询</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --error-color: #dc3545;
            --success-color: #28a745;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            /* padding: 20px; */
        }

        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        .container:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        h1 {
            text-align: center;
            margin-bottom: 25px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="email"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.2);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3a56b8;
        }

        button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }

        .result {
            margin-top: 25px;
            padding: 15px;
            border-radius: 6px;
            background-color: var(--secondary-color);
            display: none;
        }

        .result.show {
            display: block;
        }

        .result h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .result p {
            margin-bottom: 5px;
        }

        .error {
            color: var(--error-color);
        }

        .success {
            color: var(--success-color);
        }

        .loader {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loader.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .tips {
            margin: 20px;
            text-align: center;

        }

        .tips img {
            width: 270px;
            margin-top: 20px;
            border-radius: 10px;
        }
    </style>
</head>

<body>


    <!-- <div class="tips"><img src="https://img.26272829.xyz/images/2025-05-19-QLHrrBIMG_2880.JPG" alt=""></div> -->
    <!-- <div class="tips"><img src="https://img.26272829.xyz/images/2025-04-27-qCFmmmrGM95p.png" alt=""></div> -->
    <div class="container">
        <h1>验证码查询</h1>
        <form id="emailForm">
            <div class="form-group">
                <label for="email">请输入邮箱地址获取验证码</label>
                <input type="email" id="email" name="email" placeholder="例如: <EMAIL>" required>
            </div>

            <button type="submit" id="submitBtn">查询</button>
        </form>

        <div class="tips" style="margin-top: 20px;">
            <a style="color:var(--primary-color);text-decoration: none;" href="https://token.767700.xyz/" target="_blank">Cursor Token 获取器</a>
        </div>
        <div class="loader" id="loader">
            <div class="spinner"></div>
            <p>正在查询中，链路较长，请耐心等待或稍后重试...</p>
            <p class="quote" style="margin-top: 15px; font-style: italic; color: #666;"></p>
        </div>

        <div class="result" id="result">
            <h3>查询结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>



    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('emailForm');
            const loader = document.getElementById('loader');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const submitBtn = document.getElementById('submitBtn');

            // 获取URL参数中的token
            function getTokenFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('token');
            }

            form.addEventListener('submit', async function (e) {
                e.preventDefault();

                const email = document.getElementById('email').value.trim();

                if (!email) {
                    showError('请输入有效的邮箱地址');
                    return;
                }

                // 显示加载状态
                loader.classList.add('show');
                result.classList.remove('show');
                submitBtn.disabled = true;

                // 获取名言
                try {
                    const quoteResponse = await fetch('https://read.767700.xyz/');
                    const quoteData = await quoteResponse.json();
                    if (quoteData.code === 1 && quoteData.data && quoteData.data.length > 0) {
                        const quoteElement = loader.querySelector('.quote');
                        quoteElement.textContent = quoteData.data[0].value;
                    }
                } catch (error) {
                    console.error('获取名言失败:', error);
                }

                try {
                    // 构建API URL，包含token参数
                    const token = getTokenFromUrl();
                    let apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(email)}`;
                    
                    if (token) {
                        apiUrl += `&token=${encodeURIComponent(token)}`;
                    }

                    const response = await fetch(apiUrl);

                    if (!response.ok) {
                        throw new Error('未查询到相关邮件，请确认邮箱是否正确，稍后再重新查询');
                    }

                    const data = await response.json();
                    data.date = new Date(data.date).toLocaleString('zh-CN', {
                        timeZone: 'Asia/Shanghai',
                        hour12: false,  // 24小时制
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    })

                    // 隐藏加载状态
                    loader.classList.remove('show');
                    result.classList.add('show');
                    submitBtn.disabled = false;

                    if (data.result) {
                        let resultHtml = ``;

                        if (data.subject) {
                            resultHtml += `
                <h2>${data.subject}</h2>
                <div class="meta">发件人：${data.from} | 收件人：${data.to} | 时间：${data.date}</div>
            `;
                        }



                        if (data.html) {
                            resultHtml += `<p>${data.html.replace(/\n/g, '')}</p>`;
                        }
                        resultContent.innerHTML = resultHtml;
                        try {
                            // await navigator.clipboard.writeText(data.verificationCode);
                        } catch (error) {

                        }

                    } else {
                        showError('查询失败: ' + (data.message || '未知错误'));
                    }
                } catch (error) {
                    // 隐藏加载状态
                    loader.classList.remove('show');
                    submitBtn.disabled = false;

                    showError('发生错误: ' + error.message);
                }
            });

            function showError(message) {
                result.classList.add('show');
                resultContent.innerHTML = `<p class="error">${message}</p>`;
            }
        });
    </script>
</body>

</html>
