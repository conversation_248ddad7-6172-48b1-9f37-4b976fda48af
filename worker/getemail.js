#!/usr/bin/env node

// Required parameters:
// @raycast.schemaVersion 1
// @raycast.title 邮箱
// @raycast.mode fullOutput

// Optional parameters:
// @raycast.icon 📧
// @raycast.argument1 { "type": "text", "placeholder": "邮箱", "percentEncoded": true }

// Documentation:
// @raycast.description 获取邮箱
const { exec } = require('child_process');
let [topic] = process.argv.slice(2)

console.log(fetch);


const getEmail = async (topic) => {
    try {
        // 提取域名部分
        const emailDomain = decodeURIComponent(topic).substring(decodeURIComponent(topic).indexOf('@')).trim();
        const email = decodeURIComponent(topic).trim();

        console.log(emailDomain);
        console.log('完整邮箱:', email);

        let mailDetail = null;

        // 判断域名是否以 .xyz 结尾
        if (emailDomain.endsWith('.xyz')) {
            // .xyz 域名使用原有逻辑
            console.log('使用 .xyz 域名逻辑');

            // 调用临时邮箱 API
            const apiUrl = 'https://tempmail.plus/api/mails?email=<EMAIL>&limit=10&epin=4g0p8su';
            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error('获取邮件列表失败');
            }

            const data = await response.json();

            if (!data.result || !data.mail_list) {
                throw new Error('API 返回数据格式错误');
            }

            // 查找匹配的邮件
            let foundMail = null;
            for (const mail of data.mail_list) {
                if (mail.from_mail && mail.from_mail.includes(emailDomain)) {
                    foundMail = mail;
                    break;
                }
            }

            if (!foundMail) {
                console.log(`未找到来自 ${emailDomain} 的邮件`);
                return;
            }

            // 获取邮件详情
            const mailDetailUrl = `https://tempmail.plus/api/mails/${foundMail.mail_id}?email=<EMAIL>&epin=4g0p8su`;
            const detailResponse = await fetch(mailDetailUrl);

            if (!detailResponse.ok) {
                throw new Error('获取邮件详情失败');
            }

            mailDetail = await detailResponse.json();

        } else {
            // 非 .xyz 域名调用新的 API
            console.log('使用非 .xyz 域名逻辑');

            const newApiUrl = `https://mail.chatgpt.org.uk/api/get-emails?email=${encodeURIComponent(email)}`;
            console.log('调用新 API:', newApiUrl);

            const response = await fetch(newApiUrl);

            if (!response.ok) {
                throw new Error(`获取邮件失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // 检查新 API 返回格式
            if (!data.emails || !Array.isArray(data.emails) || data.emails.length === 0) {
                console.log('未找到匹配的邮件');
                return;
            }

            // 取第一封邮件并转换格式
            const firstEmail = data.emails[0];
            console.log('找到邮件:', firstEmail.subject);

            // 将新格式转换为原格式
            mailDetail = {
                attachments: [], // 新接口没有提供附件信息，设为空数组
                date: new Date(firstEmail.timestamp).toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    hour12: false,  // 24小时制
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }),
                from: firstEmail.from, // 直接使用 from 字段
                from_is_local: false, // 默认设为 false
                from_mail: firstEmail.from, // 使用 from 作为 from_mail
                from_name: firstEmail.from.includes('<') ?
                    firstEmail.from.split('<')[0].trim() :
                    firstEmail.from.split('@')[0], // 尝试提取发件人名称
                html: firstEmail.htmlContent || "", // 使用 htmlContent
                is_tls: true, // 默认设为 true
                mail_id: firstEmail.id, // 使用 id 作为 mail_id
                message_id: firstEmail.id, // 使用 id 作为 message_id
                result: true, // 设为 true 表示成功
                subject: firstEmail.subject,
                text: firstEmail.content,
                to: firstEmail.to
            };
        }

        // 确保获取到了邮件详情
        if (!mailDetail) {
            console.log('未获取到邮件详情');
            return;
        }

        function extractVerificationCode(text) {
            // 首先尝试匹配 6 位数字验证码（兼容空格）
            const digitMatch = text.match(/\b\d(?:\s\d){5}\b|\b\d{6}\b/);
            if (digitMatch) {
                return digitMatch[0].replace(/\s/g, '');
            }

            // 降级：尝试匹配 4-8 位的字母数字组合验证码（兼容空格）
            const alphanumericMatch = text.match(/\b[A-Z0-9](?:\s[A-Z0-9]){3,7}\b|\b[A-Z0-Z0-9]{4,8}\b/);
            if (alphanumericMatch) {
                return alphanumericMatch[0].replace(/\s/g, '');
            }

            return null;
        }

        // 提取邮件文本内容
        const text = mailDetail.text || '';
        console.log('邮件文本内容:');
        console.log(text);

        const verificationCode = extractVerificationCode(text);
        console.log('提取的验证码:', verificationCode);

        if (verificationCode) {
            // 使用echo命令时添加单引号来保持字符串原样输出
            exec(`echo '${verificationCode}' | pbcopy`, (error, stdout, stderr) => {
                if (error) {
                    console.error(`执行错误: ${error}`);
                    return;
                }
                console.log(stdout);
                if (stderr) {
                    console.error(`错误输出: ${stderr}`);
                    return;
                }
                // 输出成功信息
                console.log('========================');
                console.log('验证码已成功复制到剪贴板！');
                console.log('验证码内容:', verificationCode);
                console.log('操作完成！');
            });
        } else {
            console.log('未找到验证码');
            console.log('邮件主题:', mailDetail.subject || '无主题');
            console.log('发件人:', mailDetail.from || '未知');
        }

        // 显示邮件详情

    } catch (error) {
        console.log(`错误: ${error.message}`);
    }
}

getEmail(topic)