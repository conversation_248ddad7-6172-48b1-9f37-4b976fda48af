export default {
  async fetch(request, env, ctx) {
    // CORS 头部设置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Token',
      'Access-Control-Max-Age': '86400', // 24小时
    };

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      });
    }

    try {
      // 解析 URL 和查询参数
      const url = new URL(request.url);
      const token = url.searchParams.get('token');
      
      // 检查是否提供了 token 参数
      if (!token) {
        return new Response(
          JSON.stringify({ 
            error: 'Missing token parameter',
            usage: 'GET /?token=your_target_url'
          }), 
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }

      // 解码 token（如果是 URL 编码的）
      const targetUrl = decodeURIComponent(token);
      
      const response = await fetch(targetUrl, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Sec-Ch-Ua': '"Chromium";v="138", "Not=A?Brand";v="8", "Google Chrome";v="138"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"macOS"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          'Referer': 'https://app.augmentcode.com/',
          'Origin': 'https://app.augmentcode.com'
        }
      });

      // 获取响应数据
      const responseText = await response.text();
      
      // 构建返回数据
      const result = {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
        data: null,
        error: null
      };

      // 尝试解析 JSON
      if (response.ok) {
        try {
          result.data = JSON.parse(responseText);
        } catch (e) {
          result.data = responseText;
        }
      } else {
        result.error = responseText || `HTTP ${response.status}: ${response.statusText}`;
      }

      return new Response(
        JSON.stringify(result, null, 2),
        {
          status: 200, // 总是返回 200，实际状态在 result.status 中
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );

    } catch (error) {
      return new Response(
        JSON.stringify({ 
          error: 'Internal server error',
          message: error.message,
          stack: error.stack
        }), 
        { 
          status: 500,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }
  },
};