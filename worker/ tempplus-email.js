export default {
  async fetch(request, env, ctx) {
    // CORS 头部设置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24小时
    };

    // 检查 Origin 头部
    const origin = request.headers.get('Origin');
    const allowedOrigins = ['https://767700.xyz', 'https://www.trae.ai', 'https://login.augmentcode.com', 'https://app.augmentcode.com', 'https://authenticator.cursor.sh'];
    
    if (request.method !== 'OPTIONS' && (!origin || !allowedOrigins.includes(origin))) {
      return new Response(
        JSON.stringify({ 
          error: 'Access denied',
          message: 'Access denied'
        }), 
        { 
          status: 403,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // 设置允许的 Origin 头
    if (origin && allowedOrigins.includes(origin)) {
      corsHeaders['Access-Control-Allow-Origin'] = origin;
    }

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      });
    }

    try {
      // 解析 URL 和查询参数
      const url = new URL(request.url);
      const email = url.searchParams.get('email');
      const token = url.searchParams.get('token');
      
      // 检查是否提供了 email 参数
      if (!email) {
        return new Response(
          JSON.stringify({ 
            error: 'Missing email parameter',
            usage: ''
          }), 
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }
      
      // 检查是否提供了 token 参数
      if (!token) {
        return new Response(
          JSON.stringify({ 
            error: 'Missing token parameter',
            usage: ''
          }), 
          { 
            status: 400,
            headers: { 
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }
      
      // 验证 token 是否正确
      if (token !== 'e5mB5pxyzWmm' && token !== 'E567OUwuWuN') {
        return new Response(
          JSON.stringify({ 
            error: 'Invalid token',
            message: 'Token verification failed'
          }), 
          { 
            status: 401,
            headers: { 
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }
      
      // 提取域名部分（包含 @）
      const emailDomain = email.substring(email.indexOf('@'));

      // 判断域名是否以 .xyz 结尾
      if (emailDomain.endsWith('.xyz')) {
        // .xyz 域名使用原有逻辑
        // 调用临时邮箱 API
        const apiUrl = 'https://tempmail.plus/api/mails?email=<EMAIL>&limit=20&epin=4g0p8su';
        const response = await fetch(apiUrl);

        if (!response.ok) {
          return new Response(
            JSON.stringify({ error: 'Failed to fetch mail data' }),
            {
              status: 500,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            }
          );
        }

        const data = await response.json();

        // 检查 API 返回是否成功
        if (!data.result || !data.mail_list) {
          return new Response(
            JSON.stringify({ error: 'Invalid API response' }),
            {
              status: 500,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            }
          );
        }

        // 在 mail_list 中查找匹配的域名
        for (const mail of data.mail_list) {
          if (mail.from_mail && mail.from_mail.includes(emailDomain)) {
            // 找到匹配的邮件，获取详细内容
            const mailDetailUrl = `https://tempmail.plus/api/mails/${mail.mail_id}?email=<EMAIL>&epin=4g0p8su`;
            const detailResponse = await fetch(mailDetailUrl);

            if (!detailResponse.ok) {
              return new Response(
                JSON.stringify({
                  error: 'Failed to fetch mail details',
                  mail_id: mail.mail_id
                }),
                {
                  status: 500,
                  headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                  }
                }
              );
            }

            const mailDetail = await detailResponse.json();

            mailDetail.date =  mailDetail.date.toLocaleString('zh-CN', {
              timeZone: 'Asia/Shanghai',
              hour12: false,  // 24小时制
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })

            return new Response(
              JSON.stringify(mailDetail),
              {
                status: 200,
                headers: {
                  'Content-Type': 'application/json',
                  ...corsHeaders
                }
              }
            );
          }
        }

        // 如果没有找到匹配项
        return new Response(
          JSON.stringify({
            error: 'No matching email found',
            searched_domain: emailDomain,
            total_emails: data.mail_list.length
          }),
          {
            status: 404,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );

      } else {
        // 非 .xyz 域名调用新的 API
        const newApiUrl = `https://mail.chatgpt.org.uk/api/get-emails?email=${encodeURIComponent(email)}`;
        const response = await fetch(newApiUrl);

        if (!response.ok) {
          return new Response(
            JSON.stringify({
              error: 'Failed to fetch mail data from new API',
              status: response.status,
              statusText: response.statusText
            }),
            {
              status: 500,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            }
          );
        }

        const data = await response.json();

        // 检查新 API 返回格式
        if (!data.emails || !Array.isArray(data.emails) || data.emails.length === 0) {
          return new Response(
            JSON.stringify({
              error: 'No matching email found',
              searched_email: email,
              total_emails: data.emails ? data.emails.length : 0
            }),
            {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            }
          );
        }

        // 取第一封邮件并转换格式
        const firstEmail = data.emails[0];

        // 将新格式转换为原格式
        const convertedEmail = {
          attachments: [], // 新接口没有提供附件信息，设为空数组
          date: new Date(firstEmail.timestamp).toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            hour12: false,  // 24小时制
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          from: firstEmail.from, // 直接使用 from 字段
          from_is_local: false, // 默认设为 false
          from_mail: firstEmail.from, // 使用 from 作为 from_mail
          from_name: firstEmail.from.includes('<') ?
            firstEmail.from.split('<')[0].trim() :
            firstEmail.from.split('@')[0], // 尝试提取发件人名称
          html: firstEmail.htmlContent || "", // 使用 htmlContent
          is_tls: true, // 默认设为 true
          mail_id: firstEmail.id, // 使用 id 作为 mail_id
          message_id: firstEmail.id, // 使用 id 作为 message_id
          result: true, // 设为 true 表示成功
          subject: firstEmail.subject,
          text: firstEmail.content,
          to: firstEmail.to
        };

        return new Response(
          JSON.stringify(convertedEmail),
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }
      
    } catch (error) {
      return new Response(
        JSON.stringify({ 
          error: 'Internal server error',
          message: error.message 
        }), 
        { 
          status: 500,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }
  },
};