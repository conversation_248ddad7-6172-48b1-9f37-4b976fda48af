<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor <PERSON> 获取器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 1000px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ffc107, #ff8a00);
            color: #333;
            display: none;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 193, 7, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .instructions {
            background: linear-gradient(45deg, #e8f4fd, #f0f8ff);
            border: 2px solid #bee5eb;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .instructions h3 {
            color: #0c5460;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .instructions ol {
            color: #0c5460;
            line-height: 1.8;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .result-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            color: #333;
            line-height: 1.4;
        }

        .success {
            color: #28a745;
            font-weight: bold;
        }

        .error {
            color: #dc3545;
            font-weight: bold;
        }

        .info {
            color: #007bff;
            font-weight: bold;
        }

        .copy-btn {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(23, 162, 184, 0.3);
        }

        .token-display {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 8px;
            margin: 5px 0;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img src="https://www.ppanda.com/images/logo-frog-all.png" width="36px"> Cursor Token 获取器</h1>
        </div>

        <div class="form-group">
            <label for="sessionToken">WorkosCursorSessionToken:</label>
            <input 
                type="text" 
                id="sessionToken" 
                placeholder="输入 cookie 里的 WorkosCursorSessionToken"
                autocomplete="off"
            >
        </div>

        <button id="startAuthBtn" class="btn btn-primary">
            🔑 开始认证
        </button>

        <button id="pollTokenBtn" class="btn btn-secondary">
            🎯 获取 Token
        </button>

        <div class="instructions">
            <h3>📋 第一步：如何获取 WorkosCursorSessionToken?</h3>
            <ol>
                <li>登录 Cursor 官方网站</li>
                <li>打开浏览器开发者工具（F12 或右键-检查）</li>
                <li>切换到 Application（应用程序）或 Storage（存储）标签</li>
                <li>在左侧找到 Cookies，并选择 cursor.com</li>
                <li>找到名为 <code>"WorkosCursorSessionToken"</code> 的 Cookie</li>
                <li>复制它的值并粘贴到上面的输入框中</li>
            </ol>
        </div>

        <div class="instructions">
            <h3>📋 第二步：获取 accessToken</h3>
            <ol>
                <li>在上方输入框中粘贴你刚获取的 WorkosCursorSessionToken</li>
                <li>点击 "开始认证" 按钮</li>
                <li>在新打开的页面中点击 "Yes, Log In" 按钮</li>
                <li>回到此页面，点击 "获取 Token" 按钮</li>
                <li>等待自动轮询获取结果</li>
            </ol>
        </div>

        <div id="resultArea" class="result-area">
            ✨ 等待开始认证流程...
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border-radius: 15px; border: 2px solid #dee2e6;">
            <h3 style="color: #495057; margin-bottom: 20px; font-size: 1.3em;">📥 下载相关工具</h3>
            <p style="margin-bottom: 15px;">
                <a target="_blank" href="https://img.262700.xyz/images/2025-06-25-TkrTikcurs0rgo.win.zip" 
                   style="color: #667eea; text-decoration: none; font-weight: 600; padding: 10px 20px; background: rgba(102, 126, 234, 0.1); border-radius: 8px; display: inline-block; margin: 5px; transition: all 0.3s ease;"
                   onmouseover="this.style.background='rgba(102, 126, 234, 0.2)'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.background='rgba(102, 126, 234, 0.1)'; this.style.transform='translateY(0)'">
                    💻 下载 Windows 切换 Token 登录软件
                </a>
            </p>
            <p style="margin-bottom: 15px;">
                <a target="_blank" href="https://img.1953615.xyz/m1/2025-06-25-20-29-curs0rgo.mac.zip"
                   style="color: #667eea; text-decoration: none; font-weight: 600; padding: 10px 20px; background: rgba(102, 126, 234, 0.1); border-radius: 8px; display: inline-block; margin: 5px; transition: all 0.3s ease;"
                   onmouseover="this.style.background='rgba(102, 126, 234, 0.2)'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.background='rgba(102, 126, 234, 0.1)'; this.style.transform='translateY(0)'">
                    🍎 下载 Mac 切换 Token 登录软件
                </a>
            </p>
            <img width="350px" src="https://img.1953615.xyz/m1/2025-06-25-21-07-0tCsel.png" alt="使用说明图片" 
                 style="border-radius: 10px; box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); max-width: 100%; height: auto;">
        </div>
    </div>

    <script>
        let authInfo = null;

        // 从 localStorage 恢复 session token
        const savedToken = localStorage.getItem('cursor_session_token');
        if (savedToken) {
            document.getElementById('sessionToken').value = savedToken;
        }

        // 开始认证
        document.getElementById('startAuthBtn').addEventListener('click', async () => {
            const sessionToken = document.getElementById('sessionToken').value.trim();
            const resultArea = document.getElementById('resultArea');
            const startBtn = document.getElementById('startAuthBtn');
            const pollBtn = document.getElementById('pollTokenBtn');

            if (!sessionToken) {
                resultArea.innerHTML = '<span class="error">❌ 请先输入 Session Token！</span>';
                return;
            }

            // 保存 session token
            localStorage.setItem('cursor_session_token', sessionToken);

            startBtn.textContent = '🔄 初始化中...';
            startBtn.disabled = true;
            resultArea.innerHTML = '🔄 正在初始化认证流程...';

            try {
                const response = await fetch('/api/auth/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    authInfo = data;
                    
                    resultArea.innerHTML = \`
                        <div class="success">✅ 认证流程已启动！</div>
                        
                        <div style="margin: 15px 0;">
                            <strong>UUID:</strong> \${data.uuid}
                        </div>
                        
                        <div class="info">
                            📌 登录页面将在新标签页打开
                            <br>请在新页面中点击 "Yes, Log In" 按钮
                        </div>
                        
                        <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                            <small>💡 完成登录后，请点击下方 "获取 Token" 按钮</small>
                        </div>
                    \`;

                    // 打开登录页面
                    window.open(data.clientLoginUrl, '_blank');
                    
                    pollBtn.style.display = 'block';
                } else {
                    throw new Error(data.error || '初始化失败');
                }
            } catch (error) {
                resultArea.innerHTML = \`<span class="error">❌ 初始化失败: \${error.message}</span>\`;
            } finally {
                startBtn.textContent = '🔑 开始认证';
                startBtn.disabled = false;
            }
        });

        // 获取 Token
        document.getElementById('pollTokenBtn').addEventListener('click', async () => {
            if (!authInfo) {
                document.getElementById('resultArea').innerHTML = '<span class="error">❌ 请先点击 "开始认证"</span>';
                return;
            }

            const resultArea = document.getElementById('resultArea');
            const pollBtn = document.getElementById('pollTokenBtn');

            pollBtn.textContent = '🔄 轮询中...';
            pollBtn.disabled = true;
            resultArea.innerHTML = '🔄 正在轮询获取 Token，请稍候...\\n\\n💡 确保已在登录页面点击了 "Yes, Log In" 按钮';

            const maxAttempts = 20;
            let attempts = 0;

            while (attempts < maxAttempts) {
                try {
                    const response = await fetch('/api/auth/poll', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            uuid: authInfo.uuid,
                            verifier: authInfo.codeVerifier
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        const data = result.data;
                        const accessToken = data.accessToken || "";
                        const refreshToken = data.refreshToken || "";
                        const challenge = data.challenge || "";
                        const authId = data.authId || "";
                        const uuid = data.uuid || "";

                        let userId = "";
                        if (authId.includes("|")) {
                            userId = authId.split("|")[1];
                        }

                        resultArea.innerHTML = \`
                            <div class="success">🎉 Token 获取成功！</div>
                            
                            <div style="margin: 8px 0;">
                                <strong>User ID:</strong>
                                <div class="token-display">\${userId}</div>
                            </div>
                            
                            <div style="margin: 8px 0;">
                                <strong>Access Token:</strong>
                                <div class="token-display">\${accessToken}</div>
                            </div>
                            
                            <div style="margin: 8px 0;">
                                <strong>Refresh Token:</strong>
                                <div class="token-display">\${refreshToken}</div>
                            </div>
                            
                            <div style="margin: 8px 0;">
                                <strong>Challenge:</strong>
                                <div class="token-display">\${challenge}</div>
                            </div>
                            
                            <div style="margin: 8px 0;">
                                <strong>Auth ID:</strong>
                                <div class="token-display">\${authId}</div>
                            </div>
                            
                            <div style="margin: 8px 0;">
                                <strong>UUID:</strong>
                                <div class="token-display">\${uuid}</div>
                            </div>
                            
                            <button class="copy-btn" onclick="copyToClipboard('\${accessToken}', this)">
                                📋 复制 Access Token
                            </button>
                        \`;
                        
                        pollBtn.textContent = '🎯 获取 Token';
                        pollBtn.disabled = false;
                        return;
                    }

                    attempts++;
                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        resultArea.innerHTML = \`🔄 轮询中... (尝试 \${attempts}/\${maxAttempts})\\n\\n💡 确保已在登录页面点击了 "Yes, Log In" 按钮\`;
                    }
                } catch (error) {
                    console.error('轮询失败:', error);
                    attempts++;
                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                }
            }

            resultArea.innerHTML = \`
                <span class="error">❌ 轮询超时，请检查以下事项：</span>
                
                <div style="margin-top: 15px; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px;">
                    <strong>检查项目：</strong><br>
                    1. 是否在登录页面点击了 "Yes, Log In"<br>
                    2. 网络连接是否正常<br>
                    3. Session Token 是否有效<br>
                    4. 是否等待了足够的时间
                </div>
            \`;
            
            pollBtn.textContent = '🎯 获取 Token';
            pollBtn.disabled = false;
        });

        // 复制到剪贴板
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✅ 已复制到剪贴板！';
                button.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'linear-gradient(45deg, #17a2b8, #138496)';
                }, 3000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }
    </script>
</body>
</html>