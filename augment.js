const vscode = require("vscode");
const result = await vscode.window.showQuickPick([{
    label: "1. 重置机器码",
    id: "reset",
    description: "建议更换节点 IP 后再换新号登录，换号后建议开启新会话窗口。填入对应权限码重置！",
}, {
    label: "2. 官网登录",
    id: "official",
    description: "使用账号登录 " + crypto.randomUUID(),
}, {
    label: "3. 包月VIP用户登录",
    id: "vip",
    description: "直接登录写入权限"   
}, {
    label: "4. 获取库存号",
    id: "free",
    description: "免费获取共享库存号临时试用 accessToken"
},{
    label: "5. 使用 accessToken 登录",
    id: "token",
    description: "使用 accessToken 登录"
}], {
    canPickMany: false,
    ignoreFocusOut: true,
    matchOnDescription: true,
    matchOnDetail: true,
    placeHolder: "请选择你需要的操作 ",
},);
vscode.window.showInformationMessage("选择了" + result.description);
if (result.id === "reset") {
    const input = await vscode.window.showInputBox({
        prompt: '\u8bf7\u8f93\u5165\u4f60\u7684\u91cd\u7f6e\u6743\u9650\u7801',
        placeHolder: '\u5728\u8fd9\u91cc\u8f93\u5165\u60a8\u7684\u91cd\u7f6e\u6743\u9650\u7801\u002e\u002e\u002e'
    });
    const response = await fetch(`https://account.767700.xyz?token=${input}`);
    const data = await response.json();
    if (data.result && (new Date() < new Date(data.result))) {
        this._context.globalState.update("sessionId", crypto.randomUUID());
        vscode.commands.executeCommand('workbench.action.reloadWindow');
        return
    } else {
        vscode.window.showInformationMessage('\u8bf7\u4f7f\u7528\u6b63\u786e\u7684\u6743\u9650\u7801\u91cd\u7f6e', {
            modal: true
        }, '\u786e\u5b9a');
    }
    return
} else if (result.id === "free") {
    const response = await fetch(`https://account.767700.xyz?token=o2djU4f8pypm`);
    const data = await response.json();
    vscode.window.showInformationMessage(data.result, {
        modal: true
    }, '确定');
    return
} else if (result.id === "vip") {
    const input = await vscode.window.showInputBox({
        prompt: '请输入包月账号权限码',
        placeHolder: '输入包月账号权限码'
    });
    const response = await fetch(`https://account.767700.xyz?token=${input}`);
    const data = await response.json();
    if (data.result) {
        let token = JSON.parse(data.result);
        this._context.globalState.update("sessionId", crypto.randomUUID());
        await this._context.secrets.store("augment.sessions", JSON.stringify({
            accessToken: token.accessToken,
            tenantURL: token.tenantURL,
            scopes: ["email"],
        }),);
        return {
            accessToken: token.accessToken,
            tenantURL: token.tenantURL,
            scopes: ["email"],
        }
    } else {
        vscode.window.showInformationMessage('请输入正确的权限码', {
            modal: true
        }, '确定');
    }
    return
    
} else if (result.id === "token") {
    const tenantURL = await vscode.window.showInputBox({
        password: false,
        ignoreFocusOut: true,
        prompt: "请输入 tenantUrl",
    });
    const accessToken = await vscode.window.showInputBox({
        password: false,
        ignoreFocusOut: true,
        prompt: "请输入 accessToken",
    });
    this._context.globalState.update("sessionId", crypto.randomUUID());
    await this._context.secrets.store("augment.sessions", JSON.stringify({
        accessToken: accessToken,
        tenantURL: tenantURL,
        scopes: ["email"],
    }),);
    return {
        accessToken: accessToken,
        tenantURL: tenantURL,
        scopes: ["email"],
    }
}

