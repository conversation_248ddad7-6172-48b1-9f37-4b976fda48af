<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱内容临时查询</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --error-color: #dc3545;
            --success-color: #28a745;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f5f7fa;
            min-height: 100vh;
        }

        .top-bar {
            width: 100vw;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            padding: 20px 0 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .top-bar form {
            display: flex;
            gap: 10px;
            width: 100%;
            max-width: 600px;
        }

        .top-bar input[type="email"] {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .top-bar button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .top-bar button:hover {
            background-color: #3a56b8;
        }

        .main {
            display: flex;
            max-width: 1100px;
            margin: 30px auto 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }

        .sidebar {
            width: 280px;
            background: var(--secondary-color);
            border-right: 1px solid #eee;
            padding: 0;
        }

        .email-list {
            list-style: none;
        }

        .email-item {
            padding: 18px 20px;
            border-bottom: 1px solid #e5e5e5;
            cursor: pointer;
            transition: background 0.2s;
        }

        .email-item.selected {
            background: #e8edfa;
            color: var(--primary-color);
        }

        .email-item:hover {
            background: #f0f4ff;
        }

        .email-item .subject {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .email-item .from {
            font-size: 13px;
            color: #888;
        }

        .content-area {
            flex: 1;
            padding: 40px 40px 40px 40px;
            display: flex;
            flex-direction: column;
        }

        .content-area h2 {
            color: var(--primary-color);
            margin-bottom: 18px;
        }

        .content-area .meta {
            color: #9C27B0;
            font-size: 17px;
            margin-bottom: 18px;
        }

        .content-area .body {
            font-size: 16px;
            line-height: 1.8;
        }

        .error {
            color: var(--error-color);
            margin-top: 10px;
        }

        .loader {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loader.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @media (max-width: 900px) {
            .main {
                flex-direction: column;
                height: auto;
            }

            .sidebar {
                width: 100%;
                max-height: 200px;
                border-right: none;
                border-bottom: 1px solid #eee;
            }

            .content-area {
                padding: 20px;
            }
        }

        .tips {
            margin-top: 20px;
            text-align: center;

        }

        .tips img {
            width: 400px;
            margin-top: 20px;
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <div class="top-bar">
        <form id="emailForm">
            <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
            <button type="submit" id="submitBtn">查询</button>
        </form>
    </div>
    <div class="main">
        <aside class="sidebar">
            <ul class="email-list" id="emailList">
                <!-- 邮箱列表项由JS动态生成 -->
            </ul>
        </aside>
        <section class="content-area" id="contentArea">
            <div class="loader" id="loader">
                <div class="spinner"></div>
                <p>正在查询中，链路较长，繁忙时请耐心等待... <b style="color: #f41212;">请不要多次发送！</b></p>
            </div>
            <div id="emailContent">
                <h2>欢迎使用邮箱内容/验证码查询服务</h2>
                <div class="meta">在上方输入账号邮箱地址并点击查询，然后在左侧列表选择对应的邮件查看详情。</div>



                <style>
                    .tips-container {
                        max-width: 800px;
                        margin: 30px auto;
                        padding: 0 20px;
                    }

                    .tips-card {
                        background-color: #fff;
                        border-radius: 10px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }

                    .tips-card h2 {
                        color: #333;
                        text-align: center;
                        margin-bottom: 20px;
                        padding-bottom: 10px;
                        border-bottom: 2px solid #f0f0f0;
                    }

                    .tips-content {
                        display: flex;
                        flex-direction: column;
                        gap: 20px;
                    }

                    .tips-item {
                        gap: 15px;
                        padding: 15px;
                        border-radius: 8px;
                        background-color: #f9f9f9;
                    }

                    .tips-item.warning {
                        background-color: #fff8e6;
                        border-left: 4px solid #ffcc00;
                    }

                    .tips-item.solution {
                        background-color: #f0f7ff;
                        border-left: 4px solid #1890ff;
                    }

                    .tips-item.alternative {
                        background-color: #f6f6f6;
                        border-left: 4px solid #666;
                    }

                    .tips-icon {
                        font-size: 20px;
                        font-style: normal;
                    }

                    .tips-item h3 {
                        margin-top: 0;
                        margin-bottom: 10px;
                        color: #333;
                    }

                    .tips-item p {
                        margin: 0 0 10px 0;
                        line-height: 1.6;
                    }

                    .tips-item ol {
                        margin: 10px 0;
                        padding-left: 20px;
                    }

                    .tips-item li {
                        margin-bottom: 8px;
                        line-height: 1.6;
                    }

                    .tips-item code {
                        background-color: #f0f0f0;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-family: monospace;
                        font-size: 14px;
                    }

                    @media (max-width: 576px) {
                        .tips-item {
                            flex-direction: column;
                            gap: 10px;
                        }

                        .tips-icon {
                            align-self: flex-start;
                        }
                    }
                </style>
            </div>
        </section>
    </div>
    <script>
        let selectedId = null;
        const emailList = document.getElementById('emailList');
        const emailContent = document.getElementById('emailContent');
        const loader = document.getElementById('loader');
        // 渲染邮箱列表
        function renderEmailList(emails) {
            emailList.innerHTML = '';
            emails.forEach((email, idx) => {
                const li = document.createElement('li');
                li.className = 'email-item' + (selectedId === idx ? ' selected' : '');
                li.innerHTML = `<div class="subject">${email.subject}</div><div class="from">${email.from} · ${email.date}</div>`;
                li.onclick = () => {
                    selectedId = idx;
                    renderEmailList(emails);
                    showEmailContent(email);
                };
                emailList.appendChild(li);
            });
        }
        // 展示邮件内容
        function showEmailContent(email) {
            emailContent.innerHTML = `
                <h2>${email.subject}</h2>
                <div class="meta">发件人：${email.from} | 收件人：${email.to} | 时间：${email.date}</div>
                <div class="body">${email.content.replace(/\n/g, '<br>')}</div>
            `;
        }
        // 初始渲染
        // renderEmailList(mockData.data);
        // 表单提交逻辑
        document.getElementById('emailForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            const email = document.getElementById('email').value.trim();
            if (!email) return;
            loader.classList.add('show');
            // emailContent.innerHTML = '';
            selectedId = null;
            try {
                const response = await fetch(`https://api.12050231.xyz/api/email?email=${encodeURIComponent(email)}`);
                if (!response.ok) throw new Error('网络请求失败');
                const data = await response.json();
                loader.classList.remove('show');
                if (data.success && Array.isArray(data.data) && data.data.length > 0) {
                    // 根据时间先后排序，将时间字符串转换为可比较的格式
                    const sortedData = data.data.sort((a, b) => {
                        // 将日期字符串转换为Date对象进行比较
                        const dateA = new Date(a.date.replace(/(\d+)月(\d+)日\s+(\d+):(\d+):(\d+)/, '2024-$1-$2 $3:$4:$5'));
                        const dateB = new Date(b.date.replace(/(\d+)月(\d+)日\s+(\d+):(\d+):(\d+)/, '2024-$1-$2 $3:$4:$5'));
                        return dateB - dateA; // 降序排列，最新的在前面
                    });

                    renderEmailList(sortedData);
                    emailContent.innerHTML = '<h2>查询完成</h2><div class="meta">查询结果/确认返回码的接收时间是否和发送时间匹配再输入</div>';
                } else {
                    emailList.innerHTML = '';
                    emailContent.innerHTML = '<div class="meta">未查询到相关邮件，可能服务繁忙，让验证码飞一会，请稍后再重新查询。</div><p id="hitokoto"><a href= "#" id="hitokoto_text" ></a></p> ';
                    // 请注意此 Web API 的兼容性，
                    // 不支持 IE, iOS Safari < 10.1，
                    // 完整支持列表参考：https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API
                    fetch('https://v1.hitokoto.cn')
                        .then(response => response.json())
                        .then(data => {
                            const hitokoto = document.querySelector('#hitokoto')
                            hitokoto.innerText = data.hitokoto
                        })
                        .catch(console.error)
                }
            } catch (err) {
                loader.classList.remove('show');
                emailList.innerHTML = '';
                emailContent.innerHTML = `<div class='error'>查询失败：${err.message}</div>`;
            }
        });
    </script>
</body>

</html>