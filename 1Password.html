<!doctype html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <title>1Password</title>
</head>
<style>code[class*="language-"],pre[class*="language-"]{color:#ccc;background:none;font-family:Consolas,Monaco,'Andale Mono','Ubuntu Mono',monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-ms-hyphens:none;hyphens:none;}pre[class*="language-"]{padding:1em;margin:.5em 0;overflow:auto;}:not(pre) > code[class*="language-"],pre[class*="language-"]{background:#2d2d2d;}:not(pre) > code[class*="language-"]{padding:.1em;border-radius:.3em;white-space:normal;}.token.comment,.token.block-comment,.token.prolog,.token.doctype,.token.cdata{color:#999;}.token.punctuation{color:#ccc;}.token.tag,.token.attr-name,.token.namespace,.token.deleted{color:#e2777a;}.token.function-name{color:#6196cc;}.token.boolean,.token.number,.token.function{color:#f08d49;}.token.property,.token.class-name,.token.constant,.token.symbol{color:#f8c555;}.token.selector,.token.important,.token.atrule,.token.keyword,.token.builtin{color:#cc99cd;}.token.string,.token.char,.token.attr-value,.token.regex,.token.variable{color:#7ec699;}.token.operator,.token.entity,.token.url{color:#67cdcc;}.token.important,.token.bold{font-weight:bold;}.token.italic{font-style:italic;}.token.entity{cursor:help;}.token.inserted{color:green;}:root{--notion-font:ui-sans-serif,system-ui,apple-system,BlinkMacSystemFont,'Segoe UI',Helvetica,'Apple Color Emoji',Arial,sans-serif,'Segoe UI Emoji','Segoe UI Symbol';--fg-color:rgb(55,53,47);--fg-color-0:rgba(55,53,47,0.09);--fg-color-1:rgba(55,53,47,0.16);--fg-color-2:rgba(55,53,47,0.4);--fg-color-3:rgba(55,53,47,0.6);--fg-color-4:#000;--fg-color-5:rgba(55,53,47,0.024);--fg-color-6:rgba(55,53,47,0.8);--fg-color-icon:var(--fg-color);--bg-color:#fff;--bg-color-0:rgba(135,131,120,0.15);--bg-color-1:rgb(247,246,243);--bg-color-2:rgba(135,131,120,0.15);--select-color-0:rgb(46,170,220);--select-color-1:rgba(45,170,219,0.3);--select-color-2:#d9eff8;--notion-red:rgb(224,62,62);--notion-pink:rgb(173,26,114);--notion-blue:rgb(11,110,153);--notion-purple:rgb(105,64,165);--notion-teal:rgb(77,100,97);--notion-yellow:rgb(223,171,1);--notion-orange:rgb(217,115,13);--notion-brown:rgb(100,71,58);--notion-gray:rgb(155,154,151);--notion-red_background:rgb(251,228,228);--notion-pink_background:rgb(244,223,235);--notion-blue_background:rgb(221,235,241);--notion-purple_background:rgb(234,228,242);--notion-teal_background:rgb(221,237,234);--notion-yellow_background:rgb(251,243,219);--notion-orange_background:rgb(250,235,221);--notion-brown_background:rgb(233,229,227);--notion-gray_background:rgb(235,236,237);--notion-red_background_co:rgba(251,228,228,0.3);--notion-pink_background_co:rgba(244,223,235,0.3);--notion-blue_background_co:rgba(221,235,241,0.3);--notion-purple_background_co:rgba(234,228,242,0.3);--notion-teal_background_co:rgba(221,237,234,0.3);--notion-yellow_background_co:rgba(251,243,219,0.3);--notion-orange_background_co:rgba(250,235,221,0.3);--notion-brown_background_co:rgba(233,229,227,0.3);--notion-gray_background_co:rgba(235,236,237,0.3);--notion-item-blue:rgba(0,120,223,0.2);--notion-item-orange:rgba(245,93,0,0.2);--notion-item-green:rgba(0,135,107,0.2);--notion-item-pink:rgba(221,0,129,0.2);--notion-item-brown:rgba(140,46,0,0.2);--notion-item-red:rgba(255,0,26,0.2);--notion-item-yellow:rgba(233,168,0,0.2);--notion-item-default:rgba(206,205,202,0.5);--notion-item-purple:rgba(103,36,222,0.2);--notion-item-gray:rgba(155,154,151,0.4);--notion-max-width:720px;--notion-header-height:45px;}.notion *{box-sizing:border-box;}.notion{font-size:16px;line-height:1.5;color:var(--fg-color);caret-color:var(--fg-color);font-family:var(--notion-font);}.notion > *{padding:3px 0;}.notion *{-webkit-margin-before:0;margin-block-start:0;-webkit-margin-after:0;margin-block-end:0;}.notion *::-moz-selection{background:var(--select-color-1);}.notion *::selection{background:var(--select-color-1);}.notion *,.notion *:focus{outline:0;}.notion-page-content{width:100%;display:flex;flex-direction:column;}@media (min-width:1300px) and (min-height:300px){.notion-page-content-has-aside{display:flex;flex-direction:row;width:calc((100vw + var(--notion-max-width)) / 2);}.notion-page-content-has-aside .notion-aside{display:flex;}.notion-page-content-has-aside .notion-page-content-inner{width:var(--notion-max-width);max-width:var(--notion-max-width);}}.notion-page-content-inner{position:relative;display:flex;flex-direction:column;align-items:flex-start;}.notion-aside{position:-webkit-sticky;position:sticky;top:148px;z-index:101;align-self:flex-start;flex:1 1;display:none;flex-direction:column;align-items:center;}.notion-aside-table-of-contents{display:flex;flex-direction:column;align-items:center;max-height:calc(100vh - 148px - 16px);min-width:222px;overflow:auto;background:var(--bg-color);border-radius:4px;}.notion-aside-table-of-contents-header{text-transform:uppercase;font-weight:400;font-size:1.1em;word-break:break-word;}.notion-aside-table-of-contents .notion-table-of-contents-item{line-height:1;}.notion-aside-table-of-contents .notion-table-of-contents-item-indent-level-0:first-of-type{margin-top:0;}.notion-aside-table-of-contents .notion-table-of-contents-item-indent-level-0{margin-top:0.25em;}.notion-aside-table-of-contents .notion-table-of-contents-item-indent-level-1{font-size:13px;}.notion-aside-table-of-contents .notion-table-of-contents-item-indent-level-2{font-size:12px;}.notion-aside-table-of-contents .notion-table-of-contents-item-body{border:0 none;}.notion-table-of-contents-active-item{color:var(--select-color-0) !important;}.notion-app{position:relative;background:var(--bg-color);min-height:100vh;}.notion-viewport{position:relative;padding:0px;top:0;left:0;right:0;bottom:0;}.medium-zoom-overlay{z-index:300;}.medium-zoom-image{border-radius:0;}.medium-zoom-image--opened{margin:unset !important;min-width:unset !important;min-height:unset !important;z-index:301;}.notion-frame{display:flex;flex-direction:column;width:100%;height:100%;}.notion-page-scroller{position:relative;display:flex;flex-direction:column;flex-grow:1;align-items:center;min-height:calc(100vh - var(--notion-header-height));}.notion-red,.notion-red_co{color:var(--notion-red);}.notion-pink,.notion-pink_co{color:var(--notion-pink);}.notion-blue,.notion-blue_co{color:var(--notion-blue);}.notion-purple,.notion-purple_co{color:var(--notion-purple);}.notion-teal,.notion-teal_co{color:var(--notion-teal);}.notion-yellow,.notion-yellow_co{color:var(--notion-yellow);}.notion-orange,.notion-orange_co{color:var(--notion-orange);}.notion-brown,.notion-brown_co{color:var(--notion-brown);}.notion-gray,.notion-gray_co{color:var(--notion-gray);}.notion b{font-weight:600;}.notion-title{width:100%;font-size:2.5em;font-weight:600;margin-bottom:20px;line-height:1.2;}.notion-h{position:relative;display:inline-block;font-weight:600;line-height:1.3;padding:3px 2px;margin-bottom:1px;max-width:100%;white-space:pre-wrap;word-break:break-word;}.notion-h1{font-size:1.875em;margin-top:1.08em;}.notion-header-anchor{position:absolute;top:-54px;left:0;}.notion-title + .notion-h1,.notion-title + .notion-h2,.notion-title + .notion-h3{margin-top:0;}.notion-h1:first-child{margin-top:0;}.notion-h2{font-size:1.5em;margin-top:1.1em;}.notion-h3{font-size:1.25em;margin-top:1em;}.notion-h:hover .notion-hash-link{opacity:1;}.notion-hash-link{opacity:0;text-decoration:none;float:left;margin-left:-20px;padding-right:4px;fill:var(--fg-color-icon);}.notion-page-cover{display:block;-o-object-fit:cover;object-fit:cover;width:100%;height:30vh !important;min-height:30vh !important;max-height:30vh !important;padding:0;}.notion-page-cover-wrapper{width:100%;height:30vh;min-height:30vh;max-height:30vh;display:flex;justify-content:center;align-items:center;}.notion-collection-card-cover{overflow:hidden;}.notion-collection-card-cover span,.notion-collection-card-cover img{min-height:100% !important;max-height:100% !important;}.notion-page-cover-wrapper span,.notion-page-cover-wrapper img{width:100% !important;height:30vh !important;min-height:30vh !important;max-height:30vh !important;}.notion-page{position:relative;padding:0;margin:0 auto;display:flex;flex-direction:column;flex-grow:1;flex-shrink:0;align-items:flex-start;width:100%;max-width:100%;}.notion-full-page{padding-bottom:calc(max(10vh,120px));}.notion-page-no-cover{margin-top:48px !important;padding-top:96px;}.notion-page-no-cover.notion-page-no-icon{padding-top:0;}.notion-page-no-cover.notion-page-has-image-icon{padding-top:148px;}.notion-page-has-cover.notion-page-no-icon{padding-top:48px;}.notion-page-has-cover{padding-top:96px;}.notion-page-has-cover.notion-page-has-icon.notion-page-has-text-icon{padding-top:64px;}.notion-page-icon-hero{position:absolute;top:0;left:50%;display:flex;flex-direction:row;justify-content:center;}.notion-page-icon-hero.notion-page-icon-image{width:124px;height:124px;margin-left:-62px;}.notion-page-icon-hero.notion-page-icon-span{height:78px;width:78px;margin-left:-39px;}.notion-page-icon-hero .notion-page-icon{position:relative;display:block;}.notion-page-has-cover .notion-page-icon-hero.notion-page-icon-image{top:-62px;}.notion-page-has-cover .notion-page-icon-hero.notion-page-icon-span{top:-42px;}.notion-page-icon-hero.notion-page-icon-span .notion-page-icon{font-size:78px;line-height:1.1;margin-left:0;color:var(--fg-color-icon);}.notion-page-icon-hero.notion-page-icon-image .notion-page-icon{display:block;border-radius:3px;width:100%;height:100%;max-width:100%;max-height:100%;}.notion-page-icon-hero.notion-page-icon-image img{-o-object-fit:cover;object-fit:cover;}.notion-page-icon{font-family:'Apple Color Emoji','Segoe UI Emoji',NotoColorEmoji,'Noto Color Emoji','Segoe UI Symbol','Android Emoji',EmojiSymbols;font-size:1.1em;fill:var(--fg-color-6);color:var(--fg-color-icon);}.notion-search .notion-page-icon{fill:var(--fg-color-6);color:var(--fg-color);}img.notion-page-icon,svg.notion-page-icon{display:block;-o-object-fit:fill;object-fit:fill;border-radius:3px;max-width:100%;max-height:100%;}.notion-page-icon-inline{width:22px;height:22px;max-width:22px;max-height:22px;margin:0 4px;}.notion-page-icon-inline span{max-width:100%;max-height:100%;}.notion-page-icon-inline img{-o-object-fit:cover;object-fit:cover;}.notion-page{box-sizing:border-box;width:var(--notion-max-width);padding-left:calc(min(16px,8vw));padding-right:calc(min(16px,8vw));}.notion-full-width{box-sizing:border-box;--notion-max-width:calc(min(1920px,98vw));padding-left:calc(min(96px,8vw));padding-right:calc(min(96px,8vw));}.notion-small-text{font-size:14px;}.notion-quote{display:block;width:100%;white-space:pre-wrap;word-break:break-word;border-left:3px solid currentcolor;padding:0.2em 0.9em;margin:6px 0;font-size:1.2em;}.notion-hr{width:100%;margin:6px 0;padding:0;border-top:none;border-color:var(--fg-color-0);}.notion-link{color:inherit;word-break:break-word;text-decoration:inherit;border-bottom:0.05em solid;border-color:var(--fg-color-2);opacity:0.7;transition:border-color 100ms ease-in,opacity 100ms ease-in;}.notion-link:hover{border-color:var(--fg-color-6);opacity:1;}.notion-collection .notion-link{opacity:1;}.notion-blank{width:100%;min-height:1rem;padding:3px 2px;margin-top:1px;margin-bottom:1px;}.notion-page-link{display:flex;color:var(--fg-color);text-decoration:none;width:100%;height:30px;margin:1px 0;transition:background 120ms ease-in 0s;}.notion-page-link:hover{background:var(--bg-color-0);}.notion-collection-card .notion-page-link{height:unset;margin:0;transition:unset;background:unset;}.notion-icon{display:block;width:18px;height:18px;color:var(--fg-color-icon);}.notion-page-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:500;line-height:1.3;border-bottom:1px solid var(--fg-color-1);margin:4px 0;}.notion-inline-code{color:#eb5757;padding:0.2em 0.4em;background:var(--bg-color-2);border-radius:3px;font-size:85%;font-family:'SFMono-Regular',Consolas,'Liberation Mono',Menlo,Courier,monospace;}.notion-inline-underscore{text-decoration:underline;}.notion-list{margin:0;-webkit-margin-before:0.6em;margin-block-start:0.6em;-webkit-margin-after:0.6em;margin-block-end:0.6em;}.notion-list-disc{list-style-type:disc;-webkit-padding-start:1.7em;padding-inline-start:1.7em;margin-top:0;margin-bottom:0;}.notion-list-numbered{list-style-type:decimal;-webkit-padding-start:1.6em;padding-inline-start:1.6em;margin-top:0;margin-bottom:0;}.notion-list-disc li{padding-left:0.1em;}.notion-list-numbered li{padding-left:0.2em;}.notion-list li{padding:6px 0;white-space:pre-wrap;}.notion-asset-wrapper{margin:0.5rem 0;max-width:100vw;min-width:100%;align-self:center;display:flex;flex-direction:column;}.notion-asset-wrapper-image{max-width:100%;}.notion-asset-wrapper-full{max-width:100vw;}.notion-asset-wrapper img{width:100%;height:100%;max-height:100%;}.notion-asset-wrapper iframe{border:none;background:rgb(247,246,245);}.notion-text{width:100%;white-space:pre-wrap;word-break:break-word;padding:3px 2px;margin:1px 0;}.notion-text:first-child{margin-top:2px;}.notion-text-children{padding-left:1.5em;display:flex;flex-direction:column;}.notion .notion-code{font-size:85%;}.notion-code{position:relative;width:100%;padding:1em;margin:4px 0;border-radius:3px;-moz-tab-size:2;-o-tab-size:2;tab-size:2;display:block;box-sizing:border-box;overflow:auto;background:var(--bg-color-1);font-family:SFMono-Regular,Consolas,'Liberation Mono',Menlo,Courier,monospace;}.notion-code-copy{position:absolute;top:1em;right:1em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:9;transition:opacity 0.2s cubic-bezier(0.3,0,0.5,1);}.notion-code-copy-button{display:inline-block;padding:0.6em;font-size:1.25em;line-height:1em;cursor:pointer;transition:background-color 0.2s cubic-bezier(0.3,0,0.5,1),color 0.2s cubic-bezier(0.3,0,0.5,1),border-color 0.2s cubic-bezier(0.3,0,0.5,1);box-shadow:0 1px 0 rgba(27,31,36,0.04),inset 0 1px 0 rgba(255,255,255,0.25);background-color:#f6f8fa;color:#24292f;border:1px solid rgba(27,31,36,0.15);border-radius:6px;}.notion-code-copy-button:hover{background-color:#f3f4f6;border-color:rgba(27,31,36,0.15);transition-duration:0.1s;}.notion-code-copy-button:active{background:hsla(220,14%,93%,1);border-color:rgba(27,31,36,0.15);transition:none;}.notion-code .notion-code-copy{opacity:0;}.notion-code:hover .notion-code-copy{opacity:1;}.notion-code-copy-button svg{display:block;}.notion-code-copy-tooltip{pointer-events:none;position:absolute;bottom:-38px;left:0;width:100%;display:flex;flex-direction:row;justify-content:center;z-index:99;font-size:14px;}.notion-code-copy-tooltip > div{padding:6px 8px;background:#222;color:#fff;border-radius:6px;}.notion-column{display:flex;flex-direction:column;padding-top:12px;padding-bottom:12px;}.notion-column > *:first-child{margin-top:0;margin-left:0;margin-right:0;}.notion-column > *:last-child{margin-left:0;margin-right:0;margin-bottom:0;}.notion-row{display:flex;overflow:hidden;width:100%;max-width:100%;}@media (max-width:640px){.notion-row{flex-direction:column;}.notion-row .notion-column{width:100% !important;}.notion-row .notion-spacer{display:none;}}.notion-bookmark{margin:4px 0;width:100%;box-sizing:border-box;text-decoration:none;border:1px solid var(--fg-color-1);border-radius:3px;display:flex;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}.dark-mode .notion-bookmark{border-color:var(--bg-color-0);}.notion-bookmark > div:first-child{flex:4 1 180px;padding:12px 14px 14px;overflow:hidden;text-align:left;color:var(--fg-color);}.notion-bookmark-title{font-size:14px;line-height:20px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-height:24px;margin-bottom:2px;}.notion-bookmark-description{font-size:12px;line-height:16px;opacity:0.8;height:32px;overflow:hidden;}.notion-bookmark-link{display:flex;margin-top:6px;}.notion-bookmark-link-icon{width:16px;height:16px;min-width:16px;margin-right:6px;}.notion-bookmark-link-text{font-size:12px;line-height:16px;color:var(--fg-color);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}.notion-bookmark-link-icon img{max-width:100%;max-height:100%;}.notion-bookmark-image{flex:1 1 180px;position:relative;}.notion-bookmark-image > *{position:absolute !important;width:100%;height:100%;}.notion-bookmark-image span{width:100% !important;height:100% !important;max-height:100%;}.notion-column .notion-bookmark-image{display:none;}.notion-spacer{width:calc(min(32px,4vw));}.notion-spacer:last-child{display:none;}.notion-asset-object-fit{position:absolute;left:0;top:0;right:0;bottom:0;width:100%;height:100%;border-radius:1px;}.notion-image{display:block;width:100%;border-radius:1px;}.notion-asset-caption{padding:6px 0 6px 2px;white-space:pre-wrap;word-break:break-word;caret-color:var(--fg-color);font-size:14px;line-height:1.4;color:var(--fg-color-3);}.notion-callout{padding:16px 16px 16px 12px;display:inline-flex;width:100%;border-radius:3px;border-width:1px;align-items:center;box-sizing:border-box;margin:4px 0;border:1px solid var(--fg-color-0);}.dark-mode .notion-callout{border-color:var(--bg-color-2);}.notion-callout .notion-page-icon-inline{align-self:flex-start;width:24px;height:24px;line-height:24px;font-size:1.3em;}.notion-callout-text{margin-left:8px;white-space:pre-wrap;word-break:break-word;width:100%;}.notion-toggle{padding:3px 2px;}.notion-toggle > summary{cursor:pointer;outline:none;}.notion-toggle > div{margin-left:1.1em;}.notion-collection{align-self:center;min-width:100%;}.notion-collection-header{display:flex;align-items:center;height:42px;padding:4px 2px;white-space:nowrap;overflow:hidden;}.notion-collection-header-title{display:inline-flex;align-items:center;font-size:1.25em;line-height:1.2;font-weight:600;white-space:pre-wrap;word-break:break-word;margin-right:0.5em;}.notion-collection-view-dropdown{cursor:pointer;padding:6px 8px;border:0 none;border-radius:3px;transition:background 120ms ease-in 0s;background:transparent;}.notion-collection-view-dropdown:hover{background:var(--bg-color-0);}.notion-collection-view-dropdown-icon{position:relative;top:2px;margin-left:4px;}.notion-collection-view-type{display:flex;align-items:center;font-size:14px;}.notion-collection-view-type-icon{display:inline-block;width:14px;height:14px;fill:rgba(55,53,47);margin-right:6px;}.notion-collection-view-type-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color:var(--fg-color);}.notion-table{width:100vw;max-width:100vw;align-self:center;overflow:auto hidden;}.notion-table-view{position:relative;float:left;min-width:var(--notion-max-width);padding-left:0;transition:padding 200ms ease-out;}.notion-table-header{display:flex;position:absolute;z-index:82;height:33px;color:var(--fg-color-3);min-width:var(--notion-max-width);}.notion-table-header-inner{width:100%;display:inline-flex;border-top:1px solid var(--fg-color-1);border-bottom:1px solid var(--fg-color-1);}.notion-table-header-placeholder{height:34px;}.notion-table-th{display:flex;position:relative;}.notion-table-view-header-cell{display:flex;flex-shrink:0;overflow:hidden;height:32px;font-size:14px;padding:0;}.notion-table-view-header-cell-inner{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;width:100%;height:100%;padding-left:8px;padding-right:8px;border-right:1px solid var(--fg-color-0);}.notion-table-th:last-child .notion-table-view-header-cell-inner{border-right:0 none;}.notion-collection-column-title{display:flex;align-items:center;line-height:120%;min-width:0;font-size:14px;}.notion-collection-column-title-icon{display:inline-block;width:14px;height:14px;min-width:14px;min-height:14px;fill:var(--fg-color-2);margin-right:6px;}.notion-collection-column-title-body{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}.notion-table-body{position:relative;min-width:var(--notion-max-width);}.notion-table-row{display:flex;border-bottom:1px solid var(--fg-color-1);}.notion-table-cell{min-height:32px;padding:5px 8px 6px;font-size:14px;line-height:1;white-space:normal;overflow:hidden;word-break:break-word;border-right:1px solid var(--fg-color-1);}.notion-table-cell:last-child{border-right:0 none;}.notion-table-cell-title{font-weight:500;}.notion-table-cell-text{white-space:pre-wrap;}.notion-table-cell-text,.notion-table-cell-number,.notion-table-cell-url,.notion-table-cell-email,.notion-table-cell-phone_number{line-height:1.5;}.notion-table-cell-number{white-space:pre-wrap;}.notion-table-cell-select,.notion-table-cell-multi_select{padding:7px 8px 0;}.notion-property-select,.notion-property-multi_select{display:flex;flex-wrap:wrap;grid-gap:6px;gap:6px;}.notion-property-select-item,.notion-property-multi_select-item{display:flex;align-items:center;padding:0 6px;border-radius:3px;height:18px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:120%;}.notion-property-file{display:flex;flex-wrap:wrap;align-content:flex-start;}.notion-property-file img{max-height:24px;max-width:100%;margin-right:6px;}.notion-collection-card-cover .notion-property-file{height:100%;}.notion-collection-card-cover .notion-property-file img{width:100%;margin:0;max-height:100%;}.notion-collection-card .notion-property-checkbox-container{display:flex;}.notion-property-checkbox-text{display:none;}.notion-collection-card .notion-property-checkbox-text{display:inline-block;margin-left:6px;}.notion-property-checkbox{width:16px;height:16px;}.notion-property-checkbox-checked{width:16px;height:16px;background:var(--select-color-0);}.notion-property-checkbox-checked svg{position:relative;display:block;top:1px;left:1px;width:14px;height:14px;fill:#fff;}.notion-property-checkbox-unchecked{width:16px;height:16px;border:1.3px solid var(--fg-color);}.notion-gallery{align-self:center;}.notion-gallery-view{position:relative;padding-left:0;transition:padding 200ms ease-out;}.notion-gallery-grid{display:grid;position:relative;grid-template-columns:repeat(auto-fill,minmax(260px,1fr));grid-auto-rows:1fr;grid-gap:16px;gap:16px;border-top:1px solid var(--fg-color-1);padding-top:16px;padding-bottom:4px;}.notion-gallery-grid-size-small{grid-template-columns:repeat(auto-fill,minmax(180px,1fr));}.notion-gallery-grid-size-large{grid-template-columns:repeat(auto-fill,minmax(320px,1fr));}.notion-collection-card{display:flex;flex-direction:column;overflow:hidden;text-decoration:none;box-shadow:rgba(15,15,15,0.1) 0 0 0 1px,rgba(15,15,15,0.1) 0 2px 4px;border-radius:3px;background:var(--bg-color);color:var(--fg-color);transition:background 100ms ease-out 0s;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;}.notion-collection-card:hover{background:var(--bg-color-0);}.notion-collection-card-cover{position:relative;width:100%;height:190px;border-bottom:1px solid var(--fg-color-0);overflow:hidden;}.notion-collection-card-cover img{width:100%;height:100%;border-radius:1px 1px 0 0;}.notion-collection-card-cover .notion-collection-card-cover-empty{width:100%;height:100%;pointer-events:none;overflow:hidden;background:var(--fg-color-5);box-shadow:var(--fg-color-0) 0 -1px 0 0 inset;padding:8px 8px 0;}.notion-collection-card-size-small .notion-collection-card-cover{height:124px;}.notion-collection-card-body{display:flex;flex-direction:column;padding:4px 10px;}.notion-collection-card-property{padding:4px 0;white-space:nowrap;word-break:break-word;overflow:hidden;text-overflow:ellipsis;font-size:12px;}.notion-collection-card-property:first-child{font-size:14px;font-weight:500;}.notion-collection-card-property:not(:first-child){white-space:nowrap;text-overflow:clip;}.notion-collection-card-property img{max-height:18px;}.notion-list-collection{align-self:center;width:100%;max-width:100%;}.notion-list-view{position:relative;padding-left:0;transition:padding 200ms ease-out;max-width:100%;}.notion-list-body{display:flex;flex-direction:column;border-top:1px solid var(--fg-color-1);padding-top:8px;max-width:100%;overflow:hidden;}.notion-list-item{display:flex;justify-content:space-between;align-items:center;padding:0 4px;margin:1px 0;max-width:100%;overflow:hidden;}.notion-list-item-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:500;line-height:1.3;}.notion-list-item-body{display:flex;align-items:center;flex-wrap:nowrap;overflow:hidden;}.notion-list-item-property{margin-left:14px;font-size:14px;}.notion-list-item-property .notion-property-date,.notion-list-item-property .notion-property-created_time,.notion-list-item-property .notion-property-last_edited_time,.notion-list-item-property .notion-property-url{display:inline-block;color:var(--fg-color-3);font-size:12px;overflow:hidden;text-overflow:ellipsis;}.notion-board{width:100vw;max-width:100vw;align-self:center;overflow:auto hidden;}.notion-board-view{position:relative;float:left;min-width:100%;padding-left:0;transition:padding 200ms ease-out;}.notion-board-header{display:flex;position:absolute;z-index:82;height:44px;min-width:100%;}.notion-board-header-inner{display:inline-flex;border-top:1px solid var(--fg-color-1);border-bottom:1px solid var(--fg-color-1);}.notion-board-header-placeholder{height:var(--notion-header-height);}.notion-board-th{display:flex;align-items:center;font-size:14px;padding-right:16px;box-sizing:content-box;flex-shrink:0;}.notion-board-th-body{display:flex;align-items:center;font-size:14px;line-height:1.2;padding-left:2px;padding-right:4px;white-space:nowrap;overflow:hidden;}.notion-board-th-count{color:var(--fg-color-3);font-weight:500;padding:0 8px;}.notion-board-th-empty{margin-right:4px;position:relative;top:2px;}.notion-board-body{display:inline-flex;}.notion-board-group{flex:0 0 auto;padding-right:16px;box-sizing:content-box;}.notion-board-group-card{margin-bottom:8px;}.notion-board-view .notion-board-th,.notion-board-view .notion-board-group{width:260px;}.notion-board-view-size-small .notion-board-th,.notion-board-view-size-small .notion-board-group{width:180px;}.notion-board-view-size-large .notion-board-th,.notion-board-view-size-large .notion-board-group{width:320px;}.notion-board-view .notion-collection-card .notion-collection-card-cover{height:148px;}.notion-board-view-size-small .notion-collection-card .notion-collection-card-cover{height:100px;}.notion-board-view-size-large .notion-collection-card .notion-collection-card-cover{height:180px;}.notion-collection-page-properties{width:100%;display:flex;flex-direction:column;}.notion-table-of-contents{width:100%;margin:4px 0;}.notion-table-of-contents-item{color:var(--fg-color);opacity:0.7;transition:background 100ms ease-in,opacity 100ms ease-in;text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;transition:background 20ms ease-in 0s;cursor:pointer;width:100%;padding:6px;font-size:14px;line-height:1.3;display:flex;align-items:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}.notion-table-of-contents-item:hover{background:var(--bg-color-0);opacity:1;}.notion-table-of-contents-item-body{border-bottom:1px solid var(--fg-color-1);}.notion-to-do{width:100%;display:flex;flex-direction:column;}.notion-to-do-item{display:flex;align-items:center;width:100%;padding-left:2px;min-height:calc(1.5em + 3px + 3px);}.notion-to-do-children{padding-left:1.5em;}.notion-to-do-checked .notion-to-do-item{text-decoration:line-through;opacity:0.375;}.notion-to-do-body{white-space:pre-wrap;word-break:break-word;}.notion-to-do-item .notion-property-checkbox{margin-right:8px;}.notion-google-drive{width:100%;align-self:center;margin:4px 0;}.notion-google-drive-link{position:relative;display:flex;flex-direction:column;color:inherit;text-decoration:none;width:100%;border:1px solid var(--fg-color-1);border-radius:3px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;transition:background 20ms ease-in 0s;cursor:pointer;}.notion-google-drive-link:hover{background:var(--bg-color-0);}.notion-google-drive-preview{display:block;position:relative;width:100%;padding-bottom:55%;overflow:hidden;}.notion-google-drive-preview img{position:absolute;width:100%;top:0;left:0;bottom:0;right:0;-o-object-fit:cover;object-fit:cover;-o-object-position:center top;object-position:center top;}.notion-google-drive-body{width:100%;min-height:60px;padding:12px 14px 14px;overflow:hidden;border-top:1px solid var(--fg-color-1);}.notion-google-drive-body-title{font-size:14px;line-height:20px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:2px;}.notion-google-drive-body-modified-time{font-size:12px;line-height:1.3;color:var(--fg-color-3);max-height:32px;overflow:hidden;}.notion-google-drive-body-source{display:flex;align-items:center;margin-top:6px;}.notion-google-drive-body-source-icon{flex-shrink:0;background-size:cover;width:16px;height:16px;margin-right:6px;}.notion-google-drive-body-source-domain{font-size:12px;line-height:16px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}.notion-file{width:100%;margin:1px 0;}.notion-file-link{display:flex;align-items:center;padding:3px 2px;border-radius:3px;transition:background 20ms ease-in 0s;color:inherit;text-decoration:none;}.notion-file-link:hover{background:var(--bg-color-0);}.notion-file-icon{margin-right:2px;width:1.35em;display:flex;align-items:center;justify-content:center;flex-grow:0;flex-shrink:0;min-height:calc(1.5em + 3px + 3px);height:1.35em;}.notion-file-info{display:flex;align-items:baseline;}.notion-file-title{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}.notion-file-size{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color:var(--fg-color-3);font-size:12px;line-height:16px;margin-left:6px;}.notion-audio{width:100%;}.notion-audio audio{width:100%;}.notion-equation{position:relative;display:inline-flex;color:inherit;fill:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-radius:3px;transition:background 20ms ease-in 0s;}.notion-equation-inline{-webkit-user-select:all;-moz-user-select:all;-ms-user-select:all;user-select:all;}.notion-equation-block{display:flex;flex-direction:column;overflow:auto;width:100%;max-width:100%;padding:4px 8px;margin:4px 0;cursor:pointer;}.notion-equation:hover{background:var(--bg-color-0);}.notion-equation:active,.notion-equation:focus{background:var(--select-color-2);}.notion-frame .katex-display .katex{padding-right:32px;}.notion-frame .katex > .katex-html{white-space:normal;}.notion-page-title{display:inline-flex;max-width:100%;align-items:center;line-height:1.3;transition:background 120ms ease-in 0s;}.notion-page-title-icon{display:flex;align-items:center;justify-content:center;height:22px;width:22px;border-radius:3px;flex-shrink:0;}.notion-page-title .notion-page-icon-inline{margin-left:2px;margin-right:6px;}.notion-collection-card-property .notion-link{border-bottom:0 none;}.notion-collection-card-property .notion-page-title{transition:none;}.notion-collection-card-property .notion-page-title:hover{background:unset;}.notion-collection-card-property .notion-page-title-icon{margin-left:0;height:18px;width:18px;}.notion-collection-card-property .notion-page-title-text{border-bottom:0 none;}.notion-collection-card-property .notion-property-relation .notion-page-title-text{border-bottom:1px solid;}.notion-page-title-text{position:relative;top:1px;border-bottom:1px solid var(--fg-color-1);line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:500;}.notion-link .notion-page-title-text{border-bottom:0 none;}.notion-collection-row{width:100%;padding:4px 0 8px;border-bottom:1px solid var(--fg-color-0);margin-bottom:1em;}.notion-collection-row-body{display:flex;flex-direction:column;grid-gap:4px;gap:4px;}.notion-collection-row-property{display:flex;align-items:center;}.notion-collection-row-value{flex:1 1;padding:6px 8px 7px;font-size:14px;}.notion-collection-row-property .notion-collection-column-title{display:flex;align-items:center;width:160px;height:34px;color:var(--fg-color-3);padding:0 6px;}.notion-collection-row-property .notion-property{width:100%;}.notion-collection-row-property .notion-collection-column-title-icon{width:16px;height:16px;min-width:16px;min-height:16px;}.notion-collection-row-property .notion-link{border-bottom:0 none;}.notion-collection-row-property .notion-property-relation .notion-page-title-text{border-bottom:1px solid;}.notion-user{display:block;-o-object-fit:cover;object-fit:cover;border-radius:100%;width:20px;height:20px;}.notion-list-item-property .notion-property-multi_select-item{margin-bottom:0;flex-wrap:none;}.notion-list-item-property .notion-property-multi_select-item:last-of-type{margin-right:0;}.notion-toggle .notion-collection-header,.notion-toggle .notion-table-view,.notion-toggle .notion-board-view,.notion-column .notion-collection-header,.notion-column .notion-table-view,.notion-column .notion-board-view{padding-left:0 !important;padding-right:0 !important;}.notion-toggle .notion-table,.notion-toggle .notion-board,.notion-column .notion-table,.notion-column .notion-board{width:100% !important;max-width:100% !important;}@media only screen and (max-width:730px){.notion-asset-wrapper{max-width:100%;}.notion-asset-wrapper-full{max-width:100vw;}}@media (max-width:640px){.notion-bookmark-image{display:none;}}.lazy-image-wrapper{position:relative;overflow:hidden;}.lazy-image-wrapper img{position:absolute;width:100%;height:100%;-o-object-fit:cover;object-fit:cover;max-width:100%;max-height:100%;min-width:100%;min-height:100%;}.lazy-image-preview{filter:blur(20px);transform:scale(1.1);opacity:1;transition:opacity 400ms ease-in !important;transition-delay:100ms;will-change:opacity;}.lazy-image-wrapper img.lazy-image-real{position:relative;}.lazy-image-real{opacity:0;transition:opacity 400ms ease-out !important;will-change:opacity;}.lazy-image-real.medium-zoom-image{transition:transform 0.3s cubic-bezier(0.2,0,0.2,1),opacity 400ms ease-out !important;will-change:opacity,transform;}.medium-zoom-image--opened{-o-object-fit:cover;object-fit:cover;opacity:1;}.lazy-image-loaded .lazy-image-preview{opacity:0;}.lazy-image-loaded .lazy-image-real{opacity:1;}.notion-page-cover.lazy-image-wrapper{padding:0 !important;}.notion-collection-card-cover .lazy-image-wrapper{padding:0 !important;height:100%;}.notion-page-cover .lazy-image-preview,.notion-page-cover .lazy-image-real{will-change:unset !important;}.notion-page-cover .lazy-image-loaded .lazy-image-preview{opacity:1;}.notion-lite{overflow-y:auto;}.notion-lite .notion-page{width:100%;padding:0;}.notion-lite .notion-collection-header,.notion-lite .notion-table-view,.notion-lite .notion-board-view{padding-left:0 !important;padding-right:0 !important;}.notion-lite .notion-board,.notion-lite .notion-table{width:100% !important;}.notion-header{position:-webkit-sticky;position:sticky;top:0;left:0;z-index:200;width:100%;max-width:100vw;overflow:hidden;height:var(--notion-header-height);min-height:var(--notion-header-height);background:var(--bg-color);}.notion-header .notion-nav-header{position:absolute;top:0;left:0;right:0;height:100%;display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:0 12px;-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;text-size-adjust:100%;line-height:1.2;font-size:14px;grid-gap:12px;gap:12px;}.notion-header .breadcrumbs{display:flex;flex-direction:row;align-items:center;height:100%;flex-grow:0;min-width:0;}.notion-header .breadcrumb{display:inline-flex;flex-direction:row;justify-content:center;align-items:center;white-space:nowrap;text-overflow:ellipsis;color:var(--fg-color);text-decoration:none;margin:1px 0px;padding:4px 6px;border-radius:3px;transition:background 120ms ease-in 0s;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background:transparent;cursor:pointer;}.notion-header .breadcrumb .notion-page-icon-inline{font-size:18px;line-height:1.1;margin:0 6px 0 0;}.notion-header .breadcrumb .notion-page-icon-span{position:relative;top:1px;}.notion-header .searchIcon{width:14px;height:14px;color:var(--fg-color);fill:var(--fg-color);}.notion-search-button{grid-gap:8px;gap:8px;}.notion-header .breadcrumb:not(.active):hover{background:var(--bg-color-0);}.notion-header .breadcrumb:not(.active):active{background:var(--bg-color-1);}.notion-header .breadcrumb.active{cursor:default;}.notion-header .spacer{margin:0 2px;color:var(--fg-color-2);}.notion-header .button{padding:12px;}.notion-search-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(15,15,15,0.6);display:flex;justify-content:center;align-items:flex-start;z-index:1001;}.notion-search{box-shadow:rgba(15,15,15,0.05) 0px 0px 0px 1px,rgba(15,15,15,0.1) 0px 5px 10px,rgba(15,15,15,0.2) 0px 15px 40px;border-radius:3px;background:var(--bg-color);position:relative;top:90px;max-width:600px;min-height:50px;max-height:80vh;width:75%;overflow:hidden;outline:none;font-size:16px;line-height:1.5;color:rgb(55,53,47);caret-color:rgb(55,53,47);font-family:var(--notion-font);}.notion-search input{background-color:var(--bg-color);}.notion-search .quickFindMenu{display:flex;flex-direction:column;min-width:100%;max-width:calc(100vw - 24px);height:100%;max-height:80vh;min-height:50px;}.notion-search .searchBar{display:flex;flex-direction:row;align-items:center;height:52px;box-shadow:rgba(55,53,47,0.09) 0px 1px 0px;font-size:18px;line-height:27px;padding:16px;}.notion-search .searchInput{resize:none;white-space:nowrap;border:none;outline:none;flex:1 1;line-height:inherit;font-size:inherit;}.notion-search .inlineIcon{margin-right:10px;fill:var(--fg-color-2);}.notion-search .clearButton{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-radius:20px;cursor:pointer;margin-left:8px;}.notion-search .clearIcon{width:14px;height:14px;fill:var(--fg-color-2);}.notion-search .clearButton:hover .clearIcon{fill:var(--fg-color-3);}.notion-search .clearButton:active .clearIcon{fill:var(--fg-color-6);}@-webkit-keyframes spinner{to{transform:rotate(360deg);}}@keyframes spinner{to{transform:rotate(360deg);}}.notion-search .loadingIcon{-webkit-animation:spinner 0.6s linear infinite;animation:spinner 0.6s linear infinite;}.notion-search .noResultsPane{display:flex;flex-direction:column;justify-content:center;align-items:center;padding:32px 16px;}.notion-search .noResults{font-size:14px;font-weight:500;line-height:20px;color:rgba(55,53,47,0.6);}.notion-search .noResultsDetail{font-size:14px;margin-top:2px;color:rgba(55,53,47,0.4);}.notion-search .resultsFooter{box-shadow:rgba(55,53,47,0.09) 0px -1px 0px;margin-top:1px;font-size:12px;min-height:28px;color:var(--fg-color-2);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;padding:0 16px;display:flex;flex-direction:column;justify-content:center;}.notion-search .resultsCount{font-weight:500;color:var(--fg-color-3);}.notion-search .resultsPane{display:flex;flex-direction:column;height:100%;flex:1 1;overflow:auto;}.notion-search .resultsPane .result{padding:8px 14px;border-bottom:1px solid rgba(55,53,47,0.06);font-size:14px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;flex-direction:column;align-items:stretch;color:var(--fg-color);text-decoration:none;}.notion-search .resultsPane .result:hover{background:var(--bg-color-2) !important;}.notion-search .resultsPane .result:active{background:var(--fg-color-1) !important;}.notion-search .resultsPane .result{min-height:unset;height:unset;}.notion-search .resultsPane .result .notion-page-title{display:flex;}.notion-search .resultsPane .result .notion-search-result-highlight{font-size:90%;margin:4px 0 0 30px;}.notion-sync-block{width:100%;}.notion-collection-group{margin-bottom:1em;}.notion-collection-group > summary{}.notion-collection-group > summary > div{transform:scale(0.85);transform-origin:0% 50%;display:inline-flex;align-items:center;}.notion-simple-table{border:1px solid var(--fg-color-5);border-collapse:collapse;border-spacing:0;font-size:14px;}.notion-simple-table tr:first-child td{background:var(--bg-color-0);}.notion-simple-table td{border:1px solid var(--fg-color-5);padding:8px 8px;white-space:pre-wrap;}.notion-external{border-radius:3px;transition:background 120ms ease-in 0s;text-decoration:none;}.notion-external:hover{background:var(--bg-color-0);}.notion-external-block{width:100%;margin-top:4px;border:1px solid var(--fg-color-1);padding:6px 6px;display:flex;}.notion-external-mention{display:inline-flex;padding:0 4px;align-items:center;position:relative;top:3px;}.notion-external-image{width:32px;height:32px;margin:3px 12px 3px 4px;}.notion-external-mention .notion-external-image{display:inline-flex;align-items:center;width:16px;height:16px;margin:0;margin-right:0.3em;}.notion-external-description{display:flex;flex-direction:column;}.notion-external-mention .notion-external-description{display:inline-flex;flex-direction:row;align-items:center;}.notion-external-title{font-size:14px;font-weight:500;color:var(--fg-color-4);}.notion-external-mention .notion-external-title{display:inline;font-size:16px;border-bottom:0.05em solid var(--fg-color-1);}.notion-external-subtitle{font-size:12px;color:var(--fg-color-3);}.notion-external-mention .notion-external-subtitle{display:none;}.notion-yt-lite{background-color:#000;position:absolute;width:100%;height:100%;display:block;contain:content;background-position:center center;background-size:cover;cursor:pointer;}.notion-yt-lite::before{content:'';display:block;position:absolute;top:0;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==);background-position:top;background-repeat:repeat-x;width:100%;height:60px;padding-bottom:50px;transition:all 0.2s cubic-bezier(0,0,0.2,1);}.notion-yt-lite > iframe{width:100%;height:100%;position:absolute;top:0;left:0;}.notion-yt-playbtn{width:68px;height:48px;position:absolute;cursor:pointer;transform:translate3d(-50%,-50%,0);top:50%;left:50%;z-index:1;background-color:transparent;background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 68 48"><path d="M66.52 7.74c-.78-2.93-2.49-5.41-5.42-6.19C55.79.13 34 0 34 0S12.21.13 6.9 1.55c-2.93.78-4.63 3.26-5.42 6.19C.06 13.05 0 24 0 24s.06 10.95 1.48 16.26c.78 2.93 2.49 5.41 5.42 6.19C12.21 47.87 34 48 34 48s21.79-.13 27.1-1.55c2.93-.78 4.64-3.26 5.42-6.19C67.94 34.95 68 24 68 24s-.06-10.95-1.48-16.26z" fill="red"/><path d="M45 24 27 14v20" fill="white"/></svg>');filter:grayscale(100%);transition:filter 0.1s cubic-bezier(0,0,0.2,1);border:none;}.notion-yt-lite:hover > .notion-yt-playbtn,.notion-yt-youtube .notion-yt-playbtn:focus{filter:none;}.notion-yt-initialized{cursor:unset;}.notion-yt-initialized::before,.notion-yt-initialized > .notion-yt-playbtn{opacity:0;pointer-events:none;}.notion-yt-thumbnail{position:absolute;top:0;left:0;width:100%;height:100%;-o-object-fit:cover;object-fit:cover;}.notion-collection-view-dropdown-content{font-family:var(--notion-font);min-width:220;background:#fff;border-radius:6;padding:0;box-shadow:0px 10px 38px -10px rgba(22,23,24,0.35),0px 10px 20px -15px rgba(22,23,24,0.2);-webkit-animation-duration:400ms;animation-duration:400ms;-webkit-animation-timing-function:cubic-bezier(0.16,1,0.3,1);animation-timing-function:cubic-bezier(0.16,1,0.3,1);-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;will-change:transform,opacity;}.notion-collection-view-dropdown-content-item{all:unset;position:relative;height:25px;padding:7px 96px 7px 10px;line-height:1;display:flex;align-items:center;border-radius:3px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;color:rgb(55,53,47);font-size:14px;}.notion-collection-view-dropdown-content-item[data-disabled]{color:#777;pointer-events:none;cursor:unset;}.notion-collection-view-dropdown-content-item:focus,.notion-collection-view-dropdown-content-item-active{background-color:#ebfaff;}.notion-collection-view-dropdown-content-item-active-icon{position:absolute;right:0;width:32px;display:inline-flex;align-items:center;justify-content:center;}.notion-collection-view-dropdown-content-item-active-icon > svg{width:12px;}@-webkit-keyframes slideDownAndFade{0%{opacity:0;transform:translateY(-2px);}100%{opacity:1;transform:translateY(0);}}@keyframes slideDownAndFade{0%{opacity:0;transform:translateY(-2px);}100%{opacity:1;transform:translateY(0);}}.notion-collection-view-dropdown-content[data-state='open']{-webkit-animation-name:slideDownAndFade;animation-name:slideDownAndFade;}#nprogress{pointer-events:none;}#nprogress .bar{background:#0070f3;position:fixed;z-index:1031;top:0;left:0;width:100%;height:2px;}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;box-shadow:0 0 10px #0070f3,0 0 5px #0070f3;opacity:1;transform:rotate(3deg) translate(0px,-4px);}*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#EDF2F7;}::before,::after{--tw-content:'';}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";}body{margin:0;line-height:inherit;}hr{height:0;color:inherit;border-top-width:1px;}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted;}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit;}a{color:inherit;text-decoration:inherit;}b,strong{font-weight:bolder;}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;font-size:1em;}small{font-size:80%;}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline;}sub{bottom:-0.25em;}sup{top:-0.5em;}table{text-indent:0;border-color:inherit;border-collapse:collapse;}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:inherit;color:inherit;margin:0;padding:0;}button,select{text-transform:none;}button,[type='button'],[type='reset'],[type='submit']{-webkit-appearance:button;background-color:transparent;background-image:none;}:-moz-focusring{outline:auto;}:-moz-ui-invalid{box-shadow:none;}progress{vertical-align:baseline;}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto;}[type='search']{-webkit-appearance:textfield;outline-offset:-2px;}::-webkit-search-decoration{-webkit-appearance:none;}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit;}summary{display:list-item;}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0;}fieldset{margin:0;padding:0;}legend{padding:0;}ol,ul,menu{list-style:none;margin:0;padding:0;}textarea{resize:vertical;}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#CBD5E0;}input:-ms-input-placeholder,textarea:-ms-input-placeholder{opacity:1;color:#CBD5E0;}input::placeholder,textarea::placeholder{opacity:1;color:#CBD5E0;}button,[role="button"]{cursor:pointer;}:disabled{cursor:default;}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle;}img,video{max-width:100%;height:auto;}[hidden]{display:none;}[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:#fff;border-color:#A0AEC0;border-width:1px;border-radius:0px;padding-top:0.5rem;padding-right:0.75rem;padding-bottom:0.5rem;padding-left:0.75rem;font-size:1rem;line-height:1.5rem;--tw-shadow:0 0 #0000;}[type='text']:focus,[type='email']:focus,[type='url']:focus,[type='password']:focus,[type='number']:focus,[type='date']:focus,[type='datetime-local']:focus,[type='month']:focus,[type='search']:focus,[type='tel']:focus,[type='time']:focus,[type='week']:focus,[multiple]:focus,textarea:focus,select:focus{outline:2px solid transparent;outline-offset:2px;}input::-moz-placeholder,textarea::-moz-placeholder{color:#A0AEC0;opacity:1;}input:-ms-input-placeholder,textarea:-ms-input-placeholder{color:#A0AEC0;opacity:1;}input::placeholder,textarea::placeholder{color:#A0AEC0;opacity:1;}::-webkit-datetime-edit-fields-wrapper{padding:0;}::-webkit-date-and-time-value{min-height:1.5em;}summary{outline:none;}html,body{height:100%;}#__next{display:flex;flex-flow:column;mini-height:100%;}main{flex:1 1 auto;}.page-content{margin-left:auto;margin-right:auto;max-width:64rem;padding-left:2rem;padding-right:2rem;}body{font-family:"Noto Sans",system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;line-height:1.25rem;}.heading1{font-size:1.875rem;font-weight:900;line-height:2.5rem;}.heading2{font-size:1.5rem;font-weight:700;line-height:2rem;}.text-caption{font-size:0.875rem;line-height:1.25rem;}*,::before,::after{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x:;--tw-pan-y:;--tw-pinch-zoom:;--tw-scroll-snap-strictness:proximity;--tw-ordinal:;--tw-slashed-zero:;--tw-numeric-figure:;--tw-numeric-spacing:;--tw-numeric-fraction:;--tw-ring-inset:;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur:;--tw-brightness:;--tw-contrast:;--tw-grayscale:;--tw-hue-rotate:;--tw-invert:;--tw-saturate:;--tw-sepia:;--tw-drop-shadow:;--tw-backdrop-blur:;--tw-backdrop-brightness:;--tw-backdrop-contrast:;--tw-backdrop-grayscale:;--tw-backdrop-hue-rotate:;--tw-backdrop-invert:;--tw-backdrop-opacity:;--tw-backdrop-saturate:;--tw-backdrop-sepia:;}.prose{color:var(--tw-prose-body);max-width:65ch;}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0;}.pointer-events-none{pointer-events:none;}.invisible{visibility:hidden;}.static{position:static;}.fixed{position:fixed;}.absolute{position:absolute;}.relative{position:relative;}.sticky{position:-webkit-sticky;position:sticky;}.inset-0{top:0px;right:0px;bottom:0px;left:0px;}.-inset-px{top:-1px;right:-1px;bottom:-1px;left:-1px;}.inset-y-0{top:0px;bottom:0px;}.top-0{top:0px;}.right-0{right:0px;}.bottom-0{bottom:0px;}.top-40{top:10rem;}.left-64{left:16rem;}.top-28{top:7rem;}.right-8{right:2rem;}.top-96{top:24rem;}.right-32{right:8rem;}.right-4{right:1rem;}.bottom-28{bottom:7rem;}.top-4{top:1rem;}.left-0{left:0px;}.top-12{top:3rem;}.left-full{left:100%;}.top-1\/2{top:50%;}.right-full{right:100%;}.bottom-12{bottom:3rem;}.left-1\/2{left:50%;}.bottom-\[-10px\]{bottom:-10px;}.left-3{left:0.75rem;}.z-10{z-index:10;}.z-0{z-index:0;}.z-50{z-index:50;}.z-40{z-index:40;}.z-20{z-index:20;}.col-span-1{grid-column:span 1 / span 1;}.col-span-2{grid-column:span 2 / span 2;}.m-auto{margin:auto;}.m-0{margin:0px;}.m-8{margin:2rem;}.mx-auto{margin-left:auto;margin-right:auto;}.mx-4{margin-left:1rem;margin-right:1rem;}.mx-8{margin-left:2rem;margin-right:2rem;}.my-14{margin-top:3.5rem;margin-bottom:3.5rem;}.my-10{margin-top:2.5rem;margin-bottom:2.5rem;}.my-3{margin-top:0.75rem;margin-bottom:0.75rem;}.my-5{margin-top:1.25rem;margin-bottom:1.25rem;}.my-12{margin-top:3rem;margin-bottom:3rem;}.my-6{margin-top:1.5rem;margin-bottom:1.5rem;}.-my-2{margin-top:-0.5rem;margin-bottom:-0.5rem;}.-mx-4{margin-left:-1rem;margin-right:-1rem;}.my-24{margin-top:6rem;margin-bottom:6rem;}.mt-1{margin-top:0.25rem;}.mt-10{margin-top:2.5rem;}.ml-4{margin-left:1rem;}.ml-16{margin-left:4rem;}.mt-16{margin-top:4rem;}.mb-6{margin-bottom:1.5rem;}.mb-48{margin-bottom:12rem;}.mb-\[122px\]{margin-bottom:122px;}.mt-8{margin-top:2rem;}.ml-2{margin-left:0.5rem;}.mt-3{margin-top:0.75rem;}.mt-2{margin-top:0.5rem;}.mt-5{margin-top:1.25rem;}.mb-16{margin-bottom:4rem;}.mt-48{margin-top:12rem;}.mr-3{margin-right:0.75rem;}.mr-8{margin-right:2rem;}.-mt-20{margin-top:-5rem;}.mt-24{margin-top:6rem;}.mt-12{margin-top:3rem;}.ml-6{margin-left:1.5rem;}.mb-8{margin-bottom:2rem;}.mt-6{margin-top:1.5rem;}.-mb-px{margin-bottom:-1px;}.mb-32{margin-bottom:8rem;}.-ml-8{margin-left:-2rem;}.mb-12{margin-bottom:3rem;}.mt-40{margin-top:10rem;}.mr-20{margin-right:5rem;}.mb-4{margin-bottom:1rem;}.mr-1{margin-right:0.25rem;}.mb-10{margin-bottom:2.5rem;}.mt-11{margin-top:2.75rem;}.mb-28{margin-bottom:7rem;}.mb-2{margin-bottom:0.5rem;}.mb-5{margin-bottom:1.25rem;}.ml-3{margin-left:0.75rem;}.ml-8{margin-left:2rem;}.mr-16{margin-right:4rem;}.mt-4{margin-top:1rem;}.mr-10{margin-right:2.5rem;}.mt-9{margin-top:2.25rem;}.mr-4{margin-right:1rem;}.ml-20{margin-left:5rem;}.mb-1{margin-bottom:0.25rem;}.-ml-1{margin-left:-0.25rem;}.ml-1{margin-left:0.25rem;}.ml-10{margin-left:2.5rem;}.mb-40{margin-bottom:10rem;}.ml-0{margin-left:0px;}.mr-2{margin-right:0.5rem;}.ml-5{margin-left:1.25rem;}.mt-0{margin-top:0px;}.mt-28{margin-top:7rem;}.mr-6{margin-right:1.5rem;}.mb-24{margin-bottom:6rem;}.block{display:block;}.inline-block{display:inline-block;}.inline{display:inline;}.flex{display:flex;}.inline-flex{display:inline-flex;}.table{display:table;}.grid{display:grid;}.contents{display:contents;}.hidden{display:none;}.h-screen{height:100vh;}.h-16{height:4rem;}.h-6{height:1.5rem;}.h-12{height:3rem;}.h-4{height:1rem;}.h-full{height:100%;}.h-64{height:16rem;}.h-9{height:2.25rem;}.h-48{height:12rem;}.h-7{height:1.75rem;}.h-32{height:8rem;}.h-52{height:13rem;}.h-8{height:2rem;}.h-5{height:1.25rem;}.h-14{height:3.5rem;}.h-10{height:2.5rem;}.h-\[1px\]{height:1px;}.h-\[600px\]{height:600px;}.h-11{height:2.75rem;}.h-\[38px\]{height:38px;}.h-max{height:-webkit-max-content;height:-moz-max-content;height:max-content;}.h-28{height:7rem;}.h-36{height:9rem;}.h-24{height:6rem;}.max-h-60{max-height:15rem;}.min-h-full{min-height:100%;}.min-h-screen{min-height:100vh;}.min-h-\[400px\]{min-height:400px;}.min-h-\[50vh\]{min-height:50vh;}.w-6{width:1.5rem;}.w-12{width:3rem;}.w-full{width:100%;}.w-80{width:20rem;}.w-4{width:1rem;}.w-7{width:1.75rem;}.w-32{width:8rem;}.w-8{width:2rem;}.w-5{width:1.25rem;}.w-14{width:3.5rem;}.w-10{width:2.5rem;}.w-48{width:12rem;}.w-11{width:2.75rem;}.w-72{width:18rem;}.w-1\/2{width:50%;}.w-16{width:4rem;}.w-60{width:15rem;}.w-96{width:24rem;}.w-24{width:6rem;}.w-56{width:14rem;}.min-w-0{min-width:0px;}.min-w-full{min-width:100%;}.max-w-max{max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content;}.max-w-full{max-width:100%;}.max-w-md{max-width:28rem;}.max-w-5xl{max-width:64rem;}.max-w-lg{max-width:32rem;}.max-w-prose{max-width:65ch;}.max-w-3xl{max-width:48rem;}.max-w-\[640px\]{max-width:640px;}.max-w-sm{max-width:24rem;}.max-w-7xl{max-width:80rem;}.max-w-\[180px\]{max-width:180px;}.max-w-\[160px\]{max-width:160px;}.max-w-\[120px\]{max-width:120px;}.max-w-\[144px\]{max-width:144px;}.max-w-\[260px\]{max-width:260px;}.max-w-\[216px\]{max-width:216px;}.max-w-2xl{max-width:42rem;}.max-w-6xl{max-width:72rem;}.max-w-4xl{max-width:56rem;}.flex-auto{flex:1 1 auto;}.flex-1{flex:1 1;}.flex-none{flex:none;}.flex-shrink-0{flex-shrink:0;}.flex-grow{flex-grow:1;}.grow{flex-grow:1;}.origin-top-right{transform-origin:top right;}@-webkit-keyframes spin{to{transform:rotate(360deg);}}@keyframes spin{to{transform:rotate(360deg);}}.animate-spin{-webkit-animation:spin 1s linear infinite;animation:spin 1s linear infinite;}@-webkit-keyframes pulse{50%{opacity:.5;}}@keyframes pulse{50%{opacity:.5;}}.animate-pulse{-webkit-animation:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite;animation:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite;}.cursor-pointer{cursor:pointer;}.cursor-default{cursor:default;}.cursor-not-allowed{cursor:not-allowed;}.select-none{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr));}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr));}.flex-row{flex-direction:row;}.flex-row-reverse{flex-direction:row-reverse;}.flex-col{flex-direction:column;}.flex-col-reverse{flex-direction:column-reverse;}.flex-wrap{flex-wrap:wrap;}.items-start{align-items:flex-start;}.items-end{align-items:flex-end;}.items-center{align-items:center;}.items-baseline{align-items:baseline;}.justify-start{justify-content:flex-start;}.justify-end{justify-content:flex-end;}.justify-center{justify-content:center;}.justify-between{justify-content:space-between;}.justify-items-center{justify-items:center;}.gap-5{grid-gap:1.25rem;gap:1.25rem;}.gap-6{grid-gap:1.5rem;gap:1.5rem;}.gap-3{grid-gap:0.75rem;gap:0.75rem;}.gap-10{grid-gap:2.5rem;gap:2.5rem;}.gap-4{grid-gap:1rem;gap:1rem;}.gap-y-8{grid-row-gap:2rem;row-gap:2rem;}.gap-y-6{grid-row-gap:1.5rem;row-gap:1.5rem;}.self-start{align-self:flex-start;}.self-center{align-self:center;}.overflow-auto{overflow:auto;}.overflow-hidden{overflow:hidden;}.overflow-x-auto{overflow-x:auto;}.overflow-y-auto{overflow-y:auto;}.overflow-y-scroll{overflow-y:scroll;}.truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}.whitespace-nowrap{white-space:nowrap;}.whitespace-pre-line{white-space:pre-line;}.rounded-md{border-radius:0.375rem;}.rounded-lg{border-radius:0.5rem;}.rounded-full{border-radius:9999px;}.rounded-2xl{border-radius:1rem;}.rounded-3xl{border-radius:1.5rem;}.rounded{border-radius:0.25rem;}.rounded-none{border-radius:0px;}.rounded-t-md{border-top-left-radius:0.375rem;border-top-right-radius:0.375rem;}.rounded-t-lg{border-top-left-radius:0.5rem;border-top-right-radius:0.5rem;}.rounded-l-md{border-top-left-radius:0.375rem;border-bottom-left-radius:0.375rem;}.rounded-r-md{border-top-right-radius:0.375rem;border-bottom-right-radius:0.375rem;}.border{border-width:1px;}.border-2{border-width:2px;}.border-l-4{border-left-width:4px;}.border-t{border-top-width:1px;}.border-b{border-bottom-width:1px;}.border-b-2{border-bottom-width:2px;}.border-r-2{border-right-width:2px;}.border-r-0{border-right-width:0px;}.border-l-0{border-left-width:0px;}.border-solid{border-style:solid;}.border-none{border-style:none;}.border-transparent{border-color:transparent;}.border-gray-200{--tw-border-opacity:1;border-color:rgb(237 242 247 / var(--tw-border-opacity));}.border-indigo-600{--tw-border-opacity:1;border-color:rgb(79 70 229 / var(--tw-border-opacity));}.border-slate-200{--tw-border-opacity:1;border-color:rgb(226 232 240 / var(--tw-border-opacity));}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity));}.border-gray-900{--tw-border-opacity:1;border-color:rgb(26 32 44 / var(--tw-border-opacity));}.border-black{--tw-border-opacity:1;border-color:rgb(0 0 0 / var(--tw-border-opacity));}.border-indigo-500{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity));}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity));}.border-purple-900{--tw-border-opacity:1;border-color:rgb(35 0 66 / var(--tw-border-opacity));}.border-gray-400{--tw-border-opacity:1;border-color:rgb(203 213 224 / var(--tw-border-opacity));}.border-red-500{--tw-border-opacity:1;border-color:rgb(245 101 101 / var(--tw-border-opacity));}.border-gray-100{--tw-border-opacity:1;border-color:rgb(252 252 252 / var(--tw-border-opacity));}.border-teal-500{--tw-border-opacity:1;border-color:rgb(20 184 166 / var(--tw-border-opacity));}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));}.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229 / var(--tw-bg-opacity));}.bg-indigo-100{--tw-bg-opacity:1;background-color:rgb(224 231 255 / var(--tw-bg-opacity));}.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(61 70 213 / var(--tw-bg-opacity));}.bg-gray-500{--tw-bg-opacity:1;background-color:rgb(160 174 192 / var(--tw-bg-opacity));}.bg-red-100{--tw-bg-opacity:1;background-color:rgb(254 226 226 / var(--tw-bg-opacity));}.bg-red-600{--tw-bg-opacity:1;background-color:rgb(229 62 62 / var(--tw-bg-opacity));}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(252 252 252 / var(--tw-bg-opacity));}.bg-yellow-500{--tw-bg-opacity:1;background-color:rgb(253 255 157 / var(--tw-bg-opacity));}.bg-purple-900{--tw-bg-opacity:1;background-color:rgb(35 0 66 / var(--tw-bg-opacity));}.bg-\[\#00071c\]{--tw-bg-opacity:1;background-color:rgb(0 7 28 / var(--tw-bg-opacity));}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity));}.bg-zinc-50{--tw-bg-opacity:1;background-color:rgb(250 250 250 / var(--tw-bg-opacity));}.bg-blue-500{--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity));}.bg-gray-400{--tw-bg-opacity:1;background-color:rgb(203 213 224 / var(--tw-bg-opacity));}.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity));}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(237 242 247 / var(--tw-bg-opacity));}.bg-gray-300{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity));}.bg-\[\#0099ff\]{--tw-bg-opacity:1;background-color:rgb(0 153 255 / var(--tw-bg-opacity));}.bg-slate-200{--tw-bg-opacity:1;background-color:rgb(226 232 240 / var(--tw-bg-opacity));}.bg-purple-600{--tw-bg-opacity:1;background-color:rgb(145 145 171 / var(--tw-bg-opacity));}.bg-red-500{--tw-bg-opacity:1;background-color:rgb(245 101 101 / var(--tw-bg-opacity));}.bg-purple-100{--tw-bg-opacity:1;background-color:rgb(243 232 255 / var(--tw-bg-opacity));}.bg-orange-200{--tw-bg-opacity:1;background-color:rgb(254 235 200 / var(--tw-bg-opacity));}.bg-green-200{--tw-bg-opacity:1;background-color:rgb(198 246 213 / var(--tw-bg-opacity));}.bg-green-900{--tw-bg-opacity:1;background-color:rgb(0 135 107 / var(--tw-bg-opacity));}.bg-teal-50{--tw-bg-opacity:1;background-color:rgb(240 253 250 / var(--tw-bg-opacity));}.bg-teal-600{--tw-bg-opacity:1;background-color:rgb(13 148 136 / var(--tw-bg-opacity));}.bg-opacity-75{--tw-bg-opacity:0.75;}.bg-opacity-40{--tw-bg-opacity:0.4;}.bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops));}.bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops));}.from-gray-700{--tw-gradient-from:#374151;--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to,rgb(55 65 81 / 0));}.to-gray-600{--tw-gradient-to:#718096;}.bg-cover{background-size:cover;}.bg-center{background-position:center;}.bg-no-repeat{background-repeat:no-repeat;}.fill-current{fill:currentColor;}.object-cover{-o-object-fit:cover;object-fit:cover;}.p-6{padding:1.5rem;}.p-0\.5{padding:0.125rem;}.p-0{padding:0px;}.p-1{padding:0.25rem;}.p-2{padding:0.5rem;}.p-4{padding:1rem;}.px-4{padding-left:1rem;padding-right:1rem;}.py-16{padding-top:4rem;padding-bottom:4rem;}.py-2{padding-top:0.5rem;padding-bottom:0.5rem;}.py-10{padding-top:2.5rem;padding-bottom:2.5rem;}.px-5{padding-left:1.25rem;padding-right:1.25rem;}.py-3{padding-top:0.75rem;padding-bottom:0.75rem;}.px-6{padding-left:1.5rem;padding-right:1.5rem;}.py-4{padding-top:1rem;padding-bottom:1rem;}.py-6{padding-top:1.5rem;padding-bottom:1.5rem;}.px-8{padding-left:2rem;padding-right:2rem;}.px-10{padding-left:2.5rem;padding-right:2.5rem;}.py-8{padding-top:2rem;padding-bottom:2rem;}.px-12{padding-left:3rem;padding-right:3rem;}.py-12{padding-top:3rem;padding-bottom:3rem;}.py-5{padding-top:1.25rem;padding-bottom:1.25rem;}.py-1{padding-top:0.25rem;padding-bottom:0.25rem;}.px-7{padding-left:1.75rem;padding-right:1.75rem;}.py-9{padding-top:2.25rem;padding-bottom:2.25rem;}.px-2{padding-left:0.5rem;padding-right:0.5rem;}.py-1\.5{padding-top:0.375rem;padding-bottom:0.375rem;}.px-3{padding-left:0.75rem;padding-right:0.75rem;}.py-2\.5{padding-top:0.625rem;padding-bottom:0.625rem;}.px-20{padding-left:5rem;padding-right:5rem;}.py-\[10px\]{padding-top:10px;padding-bottom:10px;}.py-3\.5{padding-top:0.875rem;padding-bottom:0.875rem;}.py-24{padding-top:6rem;padding-bottom:6rem;}.pl-4{padding-left:1rem;}.pt-4{padding-top:1rem;}.pb-20{padding-bottom:5rem;}.pt-5{padding-top:1.25rem;}.pb-4{padding-bottom:1rem;}.pr-4{padding-right:1rem;}.pt-24{padding-top:6rem;}.pl-8{padding-left:2rem;}.pt-12{padding-top:3rem;}.pt-8{padding-top:2rem;}.pt-10{padding-top:2.5rem;}.pb-5{padding-bottom:1.25rem;}.pb-8{padding-bottom:2rem;}.pt-6{padding-top:1.5rem;}.pb-3{padding-bottom:0.75rem;}.pt-16{padding-top:4rem;}.pb-12{padding-bottom:3rem;}.pl-3{padding-left:0.75rem;}.pr-10{padding-right:2.5rem;}.pr-2{padding-right:0.5rem;}.pl-10{padding-left:2.5rem;}.pt-\[65\%\]{padding-top:65%;}.pb-1{padding-bottom:0.25rem;}.pl-28{padding-left:7rem;}.pb-10{padding-bottom:2.5rem;}.pb-6{padding-bottom:1.5rem;}.pr-3{padding-right:0.75rem;}.text-left{text-align:left;}.text-center{text-align:center;}.text-right{text-align:right;}.align-middle{vertical-align:middle;}.align-bottom{vertical-align:bottom;}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";}.text-4xl{font-size:2.25rem;line-height:2.5rem;}.text-base{font-size:1rem;line-height:1.5rem;}.text-sm{font-size:0.875rem;line-height:1.25rem;}.text-xs{font-size:0.75rem;line-height:1rem;}.text-2xl{font-size:1.5rem;line-height:2rem;}.text-3xl{font-size:1.875rem;line-height:2.25rem;}.text-lg{font-size:1.125rem;line-height:1.75rem;}.text-\[36px\]{font-size:36px;}.text-xl{font-size:1.25rem;line-height:1.75rem;}.text-6xl{font-size:3.75rem;line-height:1;}.text-\[10px\]{font-size:10px;}.text-5xl{font-size:3rem;line-height:1;}.font-extrabold{font-weight:800;}.font-medium{font-weight:500;}.font-bold{font-weight:700;}.font-semibold{font-weight:600;}.font-black{font-weight:900;}.font-normal{font-weight:400;}.uppercase{text-transform:uppercase;}.capitalize{text-transform:capitalize;}.italic{font-style:italic;}.leading-6{line-height:1.5rem;}.leading-normal{line-height:1.5;}.leading-\[3rem\]{line-height:3rem;}.leading-10{line-height:2.5rem;}.leading-9{line-height:2.25rem;}.leading-8{line-height:2rem;}.leading-tight{line-height:1.25;}.leading-loose{line-height:2;}.leading-4{line-height:1rem;}.tracking-tight{letter-spacing:-0.025em;}.tracking-wide{letter-spacing:0.025em;}.tracking-wider{letter-spacing:0.05em;}.tracking-tighter{letter-spacing:-0.05em;}.text-blue-300{--tw-text-opacity:1;color:rgb(147 197 253 / var(--tw-text-opacity));}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity));}.text-gray-900{--tw-text-opacity:1;color:rgb(26 32 44 / var(--tw-text-opacity));}.text-gray-500{--tw-text-opacity:1;color:rgb(160 174 192 / var(--tw-text-opacity));}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity));}.text-indigo-700{--tw-text-opacity:1;color:rgb(67 56 202 / var(--tw-text-opacity));}.text-slate-500{--tw-text-opacity:1;color:rgb(100 116 139 / var(--tw-text-opacity));}.text-gray-400{--tw-text-opacity:1;color:rgb(203 213 224 / var(--tw-text-opacity));}.text-red-600{--tw-text-opacity:1;color:rgb(229 62 62 / var(--tw-text-opacity));}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity));}.text-slate-700{--tw-text-opacity:1;color:rgb(51 65 85 / var(--tw-text-opacity));}.text-purple-900{--tw-text-opacity:1;color:rgb(35 0 66 / var(--tw-text-opacity));}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity));}.text-gray-600{--tw-text-opacity:1;color:rgb(113 128 150 / var(--tw-text-opacity));}.text-slate-900{--tw-text-opacity:1;color:rgb(15 23 42 / var(--tw-text-opacity));}.text-orange-600{--tw-text-opacity:1;color:rgb(221 107 32 / var(--tw-text-opacity));}.text-black{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity));}.text-stone-500{--tw-text-opacity:1;color:rgb(120 113 108 / var(--tw-text-opacity));}.text-gray-200{--tw-text-opacity:1;color:rgb(237 242 247 / var(--tw-text-opacity));}.text-blue-600{--tw-text-opacity:1;color:rgb(61 70 213 / var(--tw-text-opacity));}.text-indigo-500{--tw-text-opacity:1;color:rgb(99 102 241 / var(--tw-text-opacity));}.text-green-600{--tw-text-opacity:1;color:rgb(56 161 105 / var(--tw-text-opacity));}.text-\[\#0099ff\]{--tw-text-opacity:1;color:rgb(0 153 255 / var(--tw-text-opacity));}.text-orange-400{--tw-text-opacity:1;color:rgb(251 146 60 / var(--tw-text-opacity));}.text-indigo-200{--tw-text-opacity:1;color:rgb(199 210 254 / var(--tw-text-opacity));}.text-red-700{--tw-text-opacity:1;color:rgb(185 28 28 / var(--tw-text-opacity));}.text-teal-700{--tw-text-opacity:1;color:rgb(15 118 110 / var(--tw-text-opacity));}.text-teal-500{--tw-text-opacity:1;color:rgb(20 184 166 / var(--tw-text-opacity));}.underline{-webkit-text-decoration-line:underline;text-decoration-line:underline;}.line-through{-webkit-text-decoration-line:line-through;text-decoration-line:line-through;}.no-underline{-webkit-text-decoration-line:none;text-decoration-line:none;}.underline-offset-4{text-underline-offset:4px;}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}.opacity-0{opacity:0;}.opacity-100{opacity:1;}.opacity-70{opacity:0.7;}.opacity-50{opacity:0.5;}.opacity-25{opacity:0.25;}.opacity-75{opacity:0.75;}.mix-blend-multiply{mix-blend-mode:multiply;}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);}.shadow{--tw-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1),0 1px 2px -1px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);}.shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1),0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color),0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);}.transition{transition-property:color,background-color,border-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-text-decoration-color,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-text-decoration-color,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms;}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms;}.delay-150{transition-delay:150ms;}.duration-300{transition-duration:300ms;}.duration-200{transition-duration:200ms;}.duration-100{transition-duration:100ms;}.duration-75{transition-duration:75ms;}.duration-150{transition-duration:150ms;}.ease-out{transition-timing-function:cubic-bezier(0,0,0.2,1);}.ease-in{transition-timing-function:cubic-bezier(0.4,0,1,1);}.ease-in-out{transition-timing-function:cubic-bezier(0.4,0,0.2,1);}.not-full-width{width:900px;margin-left:auto;margin-right:auto;max-width:100%;align-self:center;padding-left:1.5rem;padding-right:1.5rem;padding-top:1.25rem;padding-bottom:1.25rem;}@media (min-width:640px){.notion-full-width{padding-left:6rem;padding-right:6rem;}.not-full-width{padding-left:6rem;padding-right:6rem;}}#__next{display:flex;flex-direction:column;min-height:100vh;}.last\:mr-0:last-child{margin-right:0px;}.hover\:scale-\[1\.01\]:hover{--tw-scale-x:1.01;--tw-scale-y:1.01;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}.hover\:border-indigo-800:hover{--tw-border-opacity:1;border-color:rgb(55 48 163 / var(--tw-border-opacity));}.hover\:border-gray-300:hover{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity));}.hover\:border-black:hover{--tw-border-opacity:1;border-color:rgb(0 0 0 / var(--tw-border-opacity));}.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202 / var(--tw-bg-opacity));}.hover\:bg-indigo-200:hover{--tw-bg-opacity:1;background-color:rgb(199 210 254 / var(--tw-bg-opacity));}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216 / var(--tw-bg-opacity));}.hover\:bg-red-700:hover{--tw-bg-opacity:1;background-color:rgb(185 28 28 / var(--tw-bg-opacity));}.hover\:bg-gray-200:hover{--tw-bg-opacity:1;background-color:rgb(237 242 247 / var(--tw-bg-opacity));}.hover\:bg-yellow-mega-hover:hover{--tw-bg-opacity:1;background-color:rgb(231 232 164 / var(--tw-bg-opacity));}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity));}.hover\:bg-white:hover{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));}.hover\:bg-teal-50:hover{--tw-bg-opacity:1;background-color:rgb(240 253 250 / var(--tw-bg-opacity));}.hover\:bg-gray-50:hover{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity));}.hover\:bg-teal-700:hover{--tw-bg-opacity:1;background-color:rgb(15 118 110 / var(--tw-bg-opacity));}.hover\:bg-gray-900:hover{--tw-bg-opacity:1;background-color:rgb(26 32 44 / var(--tw-bg-opacity));}.hover\:text-slate-900:hover{--tw-text-opacity:1;color:rgb(15 23 42 / var(--tw-text-opacity));}.hover\:text-gray-500:hover{--tw-text-opacity:1;color:rgb(160 174 192 / var(--tw-text-opacity));}.hover\:text-gray-700:hover{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity));}.hover\:text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity));}.hover\:text-gray-200:hover{--tw-text-opacity:1;color:rgb(237 242 247 / var(--tw-text-opacity));}.hover\:text-blue-600:hover{--tw-text-opacity:1;color:rgb(61 70 213 / var(--tw-text-opacity));}.hover\:text-red-500:hover{--tw-text-opacity:1;color:rgb(245 101 101 / var(--tw-text-opacity));}.hover\:text-teal-700:hover{--tw-text-opacity:1;color:rgb(15 118 110 / var(--tw-text-opacity));}.hover\:text-gray-900:hover{--tw-text-opacity:1;color:rgb(26 32 44 / var(--tw-text-opacity));}.hover\:underline:hover{-webkit-text-decoration-line:underline;text-decoration-line:underline;}.hover\:opacity-60:hover{opacity:0.6;}.focus\:border-indigo-500:focus{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity));}.focus\:bg-gray-400:focus{--tw-bg-opacity:1;background-color:rgb(203 213 224 / var(--tw-bg-opacity));}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px;}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);}.focus\:ring-indigo-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(99 102 241 / var(--tw-ring-opacity));}.focus\:ring-red-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(245 101 101 / var(--tw-ring-opacity));}.focus\:ring-slate-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(100 116 139 / var(--tw-ring-opacity));}.focus\:ring-white:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(255 255 255 / var(--tw-ring-opacity));}.focus\:ring-indigo-400:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(129 140 248 / var(--tw-ring-opacity));}.focus\:ring-teal-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(20 184 166 / var(--tw-ring-opacity));}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;}.focus\:ring-offset-1:focus{--tw-ring-offset-width:1px;}.focus\:ring-offset-gray-800:focus{--tw-ring-offset-color:#1f2937;}.focus-visible\:border-indigo-500:focus-visible{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity));}.focus-visible\:ring-2:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);}.focus-visible\:ring-white:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(255 255 255 / var(--tw-ring-opacity));}.focus-visible\:ring-opacity-75:focus-visible{--tw-ring-opacity:0.75;}.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;}.focus-visible\:ring-offset-orange-300:focus-visible{--tw-ring-offset-color:#fdba74;}.active\:bg-gray-400:active{--tw-bg-opacity:1;background-color:rgb(203 213 224 / var(--tw-bg-opacity));}.active\:bg-yellow-mega-active:active{--tw-bg-opacity:1;background-color:rgb(210 210 165 / var(--tw-bg-opacity));}.active\:text-gray-400:active{--tw-text-opacity:1;color:rgb(203 213 224 / var(--tw-text-opacity));}.active\:shadow-megaActive:active{--tw-shadow:-4px 4px 0px #9191AB;--tw-shadow-colored:-4px 4px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);}.group:hover .group-hover\:text-indigo-800{--tw-text-opacity:1;color:rgb(55 48 163 / var(--tw-text-opacity));}.group:hover .group-hover\:text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity));}.group:hover .group-hover\:text-indigo-500{--tw-text-opacity:1;color:rgb(99 102 241 / var(--tw-text-opacity));}.group:hover .group-hover\:text-blue-600{--tw-text-opacity:1;color:rgb(61 70 213 / var(--tw-text-opacity));}.group:hover .group-hover\:text-teal-500{--tw-text-opacity:1;color:rgb(20 184 166 / var(--tw-text-opacity));}.group:hover .group-hover\:text-gray-500{--tw-text-opacity:1;color:rgb(160 174 192 / var(--tw-text-opacity));}.dark .dark\:border-slate-200\/5{border-color:rgb(226 232 240 / 0.05);}.dark .dark\:border-transparent{border-color:transparent;}.dark .dark\:bg-slate-700{--tw-bg-opacity:1;background-color:rgb(51 65 85 / var(--tw-bg-opacity));}.dark .dark\:bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity));}.dark .dark\:text-slate-100{--tw-text-opacity:1;color:rgb(241 245 249 / var(--tw-text-opacity));}.dark .dark\:text-slate-200{--tw-text-opacity:1;color:rgb(226 232 240 / var(--tw-text-opacity));}.dark .dark\:text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity));}.dark .dark\:hover\:bg-slate-600:hover{--tw-bg-opacity:1;background-color:rgb(71 85 105 / var(--tw-bg-opacity));}.dark .dark\:hover\:text-slate-400:hover{--tw-text-opacity:1;color:rgb(148 163 184 / var(--tw-text-opacity));}.dark .dark\:hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity));}.dark .dark\:focus\:ring-slate-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(100 116 139 / var(--tw-ring-opacity));}.dark .dark\:focus\:ring-offset-gray-900:focus{--tw-ring-offset-color:#1A202C;}@media (min-width:640px){.sm\:top-64{top:16rem;}.sm\:right-auto{right:auto;}.sm\:left-96{left:24rem;}.sm\:bottom-96{bottom:24rem;}.sm\:my-8{margin-top:2rem;margin-bottom:2rem;}.sm\:mx-0{margin-left:0px;margin-right:0px;}.sm\:-mx-6{margin-left:-1.5rem;margin-right:-1.5rem;}.sm\:mx-auto{margin-left:auto;margin-right:auto;}.sm\:ml-6{margin-left:1.5rem;}.sm\:mb-0{margin-bottom:0px;}.sm\:ml-4{margin-left:1rem;}.sm\:mt-0{margin-top:0px;}.sm\:mt-4{margin-top:1rem;}.sm\:ml-3{margin-left:0.75rem;}.sm\:mt-12{margin-top:3rem;}.sm\:mb-32{margin-bottom:8rem;}.sm\:ml-0{margin-left:0px;}.sm\:ml-12{margin-left:3rem;}.sm\:ml-8{margin-left:2rem;}.sm\:mt-8{margin-top:2rem;}.sm\:ml-10{margin-left:2.5rem;}.sm\:mt-56{margin-top:14rem;}.sm\:ml-16{margin-left:4rem;}.sm\:block{display:block;}.sm\:inline-block{display:inline-block;}.sm\:flex{display:flex;}.sm\:grid{display:grid;}.sm\:hidden{display:none;}.sm\:h-screen{height:100vh;}.sm\:h-10{height:2.5rem;}.sm\:w-full{width:100%;}.sm\:w-10{width:2.5rem;}.sm\:w-auto{width:auto;}.sm\:w-1\/2{width:50%;}.sm\:w-60{width:15rem;}.sm\:max-w-lg{max-width:32rem;}.sm\:max-w-md{max-width:28rem;}.sm\:max-w-\[70\%\]{max-width:70%;}.sm\:max-w-3xl{max-width:48rem;}.sm\:max-w-xl{max-width:36rem;}.sm\:flex-auto{flex:1 1 auto;}.sm\:flex-none{flex:none;}.sm\:translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}.sm\:scale-95{--tw-scale-x:.95;--tw-scale-y:.95;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}.sm\:scale-100{--tw-scale-x:1;--tw-scale-y:1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr));}.sm\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr));}.sm\:flex-row{flex-direction:row;}.sm\:flex-row-reverse{flex-direction:row-reverse;}.sm\:flex-col{flex-direction:column;}.sm\:items-start{align-items:flex-start;}.sm\:items-center{align-items:center;}.sm\:justify-start{justify-content:flex-start;}.sm\:justify-between{justify-content:space-between;}.sm\:gap-16{grid-gap:4rem;gap:4rem;}.sm\:gap-x-4{grid-column-gap:1rem;-moz-column-gap:1rem;column-gap:1rem;}.sm\:gap-y-8{grid-row-gap:2rem;row-gap:2rem;}.sm\:space-y-0 >:not([hidden]) ~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse));}.sm\:space-x-4 >:not([hidden]) ~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)));}.sm\:space-x-10 >:not([hidden]) ~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2.5rem * var(--tw-space-x-reverse));margin-left:calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));}.sm\:space-y-4 >:not([hidden]) ~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse));}.sm\:self-center{align-self:center;}.sm\:overflow-hidden{overflow:hidden;}.sm\:rounded-none{border-radius:0px;}.sm\:border-l{border-left-width:1px;}.sm\:border-gray-200{--tw-border-opacity:1;border-color:rgb(237 242 247 / var(--tw-border-opacity));}.sm\:border-transparent{border-color:transparent;}.sm\:border-slate-200{--tw-border-opacity:1;border-color:rgb(226 232 240 / var(--tw-border-opacity));}.sm\:bg-transparent{background-color:transparent;}.sm\:p-0{padding:0px;}.sm\:p-6{padding:1.5rem;}.sm\:p-10{padding:2.5rem;}.sm\:px-6{padding-left:1.5rem;padding-right:1.5rem;}.sm\:py-24{padding-top:6rem;padding-bottom:6rem;}.sm\:px-8{padding-left:2rem;padding-right:2rem;}.sm\:px-\[calc\(16\.667\%\+16px\)\]{padding-left:calc(16.667% + 16px);padding-right:calc(16.667% + 16px);}.sm\:px-0{padding-left:0px;padding-right:0px;}.sm\:px-20{padding-left:5rem;padding-right:5rem;}.sm\:pl-6{padding-left:1.5rem;}.sm\:pl-4{padding-left:1rem;}.sm\:pb-6{padding-bottom:1.5rem;}.sm\:pt-6{padding-top:1.5rem;}.sm\:pr-9{padding-right:2.25rem;}.sm\:pt-16{padding-top:4rem;}.sm\:pr-6{padding-right:1.5rem;}.sm\:pb-16{padding-bottom:4rem;}.sm\:text-left{text-align:left;}.sm\:text-center{text-align:center;}.sm\:align-middle{vertical-align:middle;}.sm\:text-5xl{font-size:3rem;line-height:1;}.sm\:text-sm{font-size:0.875rem;line-height:1.25rem;}.sm\:text-2xl{font-size:1.5rem;line-height:2rem;}.sm\:text-base{font-size:1rem;line-height:1.5rem;}.sm\:text-4xl{font-size:2.25rem;line-height:2.5rem;}}@media (min-width:1024px){.lg\:absolute{position:absolute;}.lg\:inset-y-0{top:0px;bottom:0px;}.lg\:col-span-3{grid-column:span 3 / span 3;}.lg\:col-span-9{grid-column:span 9 / span 9;}.lg\:-mx-8{margin-left:-2rem;margin-right:-2rem;}.lg\:mt-0{margin-top:0px;}.lg\:mt-56{margin-top:14rem;}.lg\:mt-16{margin-top:4rem;}.lg\:block{display:block;}.lg\:grid{display:grid;}.lg\:h-full{height:100%;}.lg\:w-2\/5{width:40%;}.lg\:w-full{width:100%;}.lg\:max-w-2xl{max-width:42rem;}.lg\:max-w-none{max-width:none;}.lg\:max-w-5xl{max-width:64rem;}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr));}.lg\:grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr));}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr));}.lg\:flex-row{flex-direction:row;}.lg\:items-center{align-items:center;}.lg\:gap-5{grid-gap:1.25rem;gap:1.25rem;}.lg\:gap-x-8{grid-column-gap:2rem;-moz-column-gap:2rem;column-gap:2rem;}.lg\:space-y-0 >:not([hidden]) ~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse));}.lg\:divide-y-0 >:not([hidden]) ~:not([hidden]){--tw-divide-y-reverse:0;border-top-width:calc(0px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(0px * var(--tw-divide-y-reverse));}.lg\:divide-x >:not([hidden]) ~:not([hidden]){--tw-divide-x-reverse:0;border-right-width:calc(1px * var(--tw-divide-x-reverse));border-left-width:calc(1px * calc(1 - var(--tw-divide-x-reverse)));}.lg\:from-white{--tw-gradient-from:#fff;--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to,rgb(255 255 255 / 0));}.lg\:p-20{padding:5rem;}.lg\:px-8{padding-left:2rem;padding-right:2rem;}.lg\:py-32{padding-top:8rem;padding-bottom:8rem;}.lg\:py-24{padding-top:6rem;padding-bottom:6rem;}.lg\:pt-24{padding-top:6rem;}.lg\:pb-16{padding-bottom:4rem;}.lg\:pb-8{padding-bottom:2rem;}.lg\:pb-24{padding-bottom:6rem;}.lg\:text-6xl{font-size:3.75rem;line-height:1;}}@media (min-width:1280px){.xl\:mb-10{margin-bottom:2.5rem;}.xl\:px-12{padding-left:3rem;padding-right:3rem;}}.custom-notion .notion-page{width:unset;padding-left:0 !important;padding-right:0 !important;} </style>
<body>
  
  <div class="flex flex-col items-center mx-8 sm:mx-0">
  <div class="mx-auto inline-flex items-center rounded-full px-5 mt-12 h-12 w-80 border-2 border-gray-900"><svg width="24"
      height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19 10H20C20.2652 10 20.5196 10.1054 20.7071 10.2929C20.8946 10.4804 21 10.7348 21 11V21C21 21.2652 20.8946 21.5196 20.7071 21.7071C20.5196 21.8946 20.2652 22 20 22H4C3.73478 22 3.48043 21.8946 3.29289 21.7071C3.10536 21.5196 3 21.2652 3 21V11C3 10.7348 3.10536 10.4804 3.29289 10.2929C3.48043 10.1054 3.73478 10 4 10H5V9C5 8.08075 5.18106 7.1705 5.53284 6.32122C5.88463 5.47194 6.40024 4.70026 7.05025 4.05025C7.70026 3.40024 8.47194 2.88463 9.32122 2.53284C10.1705 2.18106 11.0807 2 12 2C12.9193 2 13.8295 2.18106 14.6788 2.53284C15.5281 2.88463 16.2997 3.40024 16.9497 4.05025C17.5998 4.70026 18.1154 5.47194 18.4672 6.32122C18.8189 7.1705 19 8.08075 19 9V10ZM5 12V20H19V12H5ZM17 10V9C17 7.67392 16.4732 6.40215 15.5355 5.46447C14.5979 4.52678 13.3261 4 12 4C10.6739 4 9.40215 4.52678 8.46447 5.46447C7.52678 6.40215 7 7.67392 7 9V10H17Z"
        fill="#1A202C"></path>
    </svg>
    <input id="kc" type="text" class="ml-4 text-gray-900 border-none" placeholder="请输入您的唯一秘钥"/>
  </div>
  </div>
  <div class="text-center items-center sm:mx-0 mt-12">
    <span>加密密码：</span>
    <input id="psw_e" type="text" placeholder="请输入需要加密的密码">
    <button class="h-10 rounded-lg bg-blue-500 px-4 text-center text-base font-bold text-white outline-none hover:text-gray-200 focus:outline-none active:text-gray-400 self-center" id="encrypt">加密</button>
  </div>
<div class="text-center items-center sm:mx-0 mt-10">
  <span>解密密码：</span>
  <input id="psw" type="text" placeholder="请输入加密后的密码">
  <button class="h-10 rounded-lg bg-green-900 px-4 text-center text-base font-bold text-white outline-none hover:text-gray-200 focus:outline-none active:text-gray-400 self-center" id="decrypt">解密</button>
</div>

<!-- <p class="text-center mt-2 text-gray-700">加密解密后的文本：</p> -->
<div><h3 class="mt-8 token function max-w-prose m-auto notion-h2 text-center "><span class="text-gray-500">此处显示加密/解密后的文本</span></h3></div>
<div class="px-4 max-w-prose m-auto text-center mt-11 text-gray-700">
  1Password 是存储备份强密码的最简单方法。您只需要记住一个密码，即秘钥。您的所有其他密码会被秘钥保护。
    <p class="mt-2">您可以把加密后的密码保存在本地备份，保证安全的前提下避免了多个账户密码经常忘记和混淆的问题。</p>
    <p class="mt-2">使用本功能，只需两步就可获得您加密/解密后的密码。</p>
    <p class="mt-2">本功能完全本地加密/解密，可以安全使用</p>
    <img class="mt-1" src="https://sweixinfile.hisense.com/media/M00/75/1B/Ch4FyGQZX6OAap-DAAGNmEjQEes999.png" alt="">
  <img class="mt-4" style="border-radius: 7px;;"
    src="https://sweixinfile.hisense.com/media/M00/75/1B/Ch4FyGQZWqiAHvR-AACIRgtLSFE786.png" alt="">
</div>
<script src="https://oss.suning.com/cms/staticfilebucket/res/public/crypto-js.min.js"></script>
  <script type="text/javascript">
    let psw = document.querySelector("#psw");
    let psw_e = document.querySelector("#psw_e");
    let keyDom = document.querySelector("#kc");
    let flag = true;

    // var key = "3297bfe6-242b-46f5-b6c1-76e3d7fd";    // 密钥
    var message = '34404229877';    // 需要加密的信息
    // 加密实现
    function encrypt(plaintext) {
      var encrypted = CryptoJS.AES.encrypt(plaintext, keyDom.value);
      var ciphertext = encrypted.toString();
      return ciphertext;
    }
    // 解密实现
    function decrypt(ciphertext) {
      var plaintext = CryptoJS.AES.decrypt(ciphertext, keyDom.value).toString(CryptoJS.enc.Utf8);
      return plaintext;
    }
    function render(params) {
      document.querySelector("h3").innerHTML = params;
    }
    function checkValue(key, value) {
      if (!key || !value) {
        alert("请输入您对应的值")
        flag = false;
      }else{
        flag = true;
      }
    }
    var ciphertext = encrypt(message);
    var plaintext = decrypt(ciphertext);
    

    document.querySelector('#decrypt').addEventListener("click", function (params) {
      checkValue(psw.value, keyDom.value)
      let plaintext = decrypt(psw.value, keyDom.value);
      flag && render(plaintext)
    }, false)


    document.querySelector('#encrypt').addEventListener("click", function (params) {
      checkValue(psw_e.value, keyDom.value)
      let plaintext = encrypt(psw_e.value, keyDom.value);
      flag && render(plaintext)
    }, false)
    console.log('等我有空了要用 svelte 重写一遍');
  </script>
</body>

</html>
