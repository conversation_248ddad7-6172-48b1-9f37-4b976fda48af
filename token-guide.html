<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token 获取指南</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --bg-color: #f8fafc;
            --text-color: #334155;
            --code-bg: #1e293b;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 2rem;
            font-size: 2rem;
            text-align: center;
        }

        .step {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: white;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }

        .step-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .code-block {
            position: relative;
            margin: 1rem 0;
            background: var(--code-bg);
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .code-content {
            color: #e2e8f0;
            font-family: 'Fira Code', monospace;
            white-space: pre-wrap;
            word-break: break-all;
            margin-right: 2rem;
        }

        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: background 0.3s;
        }

        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .note {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.25rem;
        }

        @media (max-width: 640px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div style="text-align: center;">
        <img src="https://img.26272829.xyz/images/2025-05-15-R3oVVC2tExYK.png" style="max-width: 1200px; margin: 10px auto;"
            alt="">
    </div>
    <div class="container">
        <h1>Token 获取指南</h1>

        <div class="step">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">登录 <a href="https://www.cursor.com/settings">https://www.cursor.com/settings</a></div>
            </div>
            <p>按 F12 键或右键点击页面选择"检查"打开开发者工具</p>
        </div>

        <div class="step">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">找到 Cookie</div>
            </div>
            <p>在开发者工具中，导航到：</p>
            <ul style="list-style: none; margin: 1rem 0;">
                <li>➜ 应用 (Application)</li>
                <li>➜ Cookie</li>
                <li>➜ 找到并复制 WorkosCursorSessionToken 的值</li>
            </ul>
        </div>

        <div class="step">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">示例</div>
            </div>
            <p>复制的值可能类似这样：</p>
            <div class="code-block">
                <div class="code-content">user_01JQAEG6NGJG0S1F6N25NVKH8M%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlFBRUc2TkdKRzBTMUY2TjI1TlZLSDhNIiwidGltZSI6IjE3NDMwMzM5NzIiLCJyYW5kb21uZXNzIjoiMjgyOGU5NDctMjA1Yi00ZjQzIiwiZXhwIjo0MzM1MDMzOTcyLCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.9xhQfOrk_l7fcaaDDmzP7mADUcxuMfUdCgDFrlyXmaA</div>
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
            </div>
        </div>

        <div class="step">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">获取最终 Token</div>
            </div>
            <p>删除字符串开头到第一个 "ey" 之前的所有内容（包括 %3A%3A），最终得到的 Token 应该是这样，最后将 token 填入到软件中：</p>
            <div class="code-block">
                <div class="code-content">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlFBRUc2TkdKRzBTMUY2TjI1TlZLSDhNIiwidGltZSI6IjE3NDMwMzM5NzIiLCJyYW5kb21uZXNzIjoiMjgyOGU5NDctMjA1Yi00ZjQzIiwiZXhwIjo0MzM1MDMzOTcyLCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.9xhQfOrk_l7fcaaDDmzP7mADUcxuMfUdCgDFrlyXmaA</div>
                <button class="copy-btn" onclick="copyCode(this)">复制</button>
            </div>
            <div class="note">
                <strong>提示：</strong> Token 总是以 "ey" 开头，这是 JWT 格式的特征。
            </div>
            
        </div>
    
    </div>
    
    <script>
        function copyCode(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制！';
                button.style.background = 'rgba(34, 197, 94, 0.2)';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(255, 255, 255, 0.1)';
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                button.textContent = '复制失败';
                button.style.background = 'rgba(239, 68, 68, 0.2)';
                
                setTimeout(() => {
                    button.textContent = '复制';
                    button.style.background = 'rgba(255, 255, 255, 0.1)';
                }, 2000);
            });
        }
    </script>
</body>
</html> 