// 精简版：只保留获取账号按钮功能
(() => {
    console.log('hello...world...,I am from ', location.href);
  function hideElementById(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.style.display = 'none'; // 隐藏元素 
    } else {
      console.warn(`Element with id '${elementId}' not found.`); // 可选：处理元素未找到的情况
    }
  }

  hideElementById("header")
    // 显示加载状态
    function showLoading() {
        const loadingId = 'globalLoading';
        let loading = document.getElementById(loadingId);
        
        if (!loading) {
            loading = document.createElement('div');
            loading.id = loadingId;
            loading.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 99999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                        <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 15px;"></div>
                        <div style="font-size: 16px; color: #333;">正在获取账号...</div>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
            document.body.appendChild(loading);
        } else {
            loading.style.display = 'block';
        }
    }

    // 隐藏加载状态
    function hideLoading() {
        const loading = document.getElementById('globalLoading');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    // 获取账号函数
    async function getCode() {
        showLoading();
        
        try {
            const response = await fetch('https://api.12050231.xyz/api/cursor');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            
            const apiData = await response.json();
            const emailAddress = apiData.data || '';
            
            if (!emailAddress) throw new Error('API返回的data字段为空');
            
            const randomEmail = `账号：${emailAddress}

注意：一定要看教程！一定要看教程！一定要看教程！https://767700.xyz?token=E567OUwuWuN

从 Cursor 软件登录，然后选择验证码（Email sign-in code）登录。非第一次使用要重置完机器码，再登录账号哦 🟢接收验证码页面下面有重置方案🟢。

可到 https://767700.xyz?token=E567OUwuWuN 自行接收验证码。【建议使用页尾 token 方案登录，更稳定】。
`;

            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(randomEmail);
                const textarea = document.querySelector('textarea.css-apn68');
                if (textarea) textarea.value = randomEmail;
            } else {
                alert(`浏览器不支持自动复制，请手动复制:\n${randomEmail}`);
            }
            
            hideLoading();
            return randomEmail;
            
        } catch (error) {
            console.error('获取邮箱失败:', error);
            
            const topics = [
                "717200.xyz", "386000.xyz", "276000.xyz", "372000.xyz", "725000.xyz",
                "263000.xyz", "397000.xyz", "785000.xyz", "821000.xyz", "827000.xyz"
            ];
            
            const topic = topics[Math.floor(Math.random() * topics.length)];
            const randomString = Math.random().toString(36).substring(2, 15);
            const fallbackEmail = `账号：${randomString}@${topic}

从 Cursor 软件登录，然后选择验证码（Email sign-in code）登录。

非第一次使用要重置完机器码，再登录账号哦 🟢接收验证码页面下面有重置方案🟢。

可到 https://767700.xyz?token=E567OUwuWuN 自行接收验证码。【建议使用页尾 token 方案登录，更稳定】。

登录后点击官网右上角头像可查（Pro trail）用量信息。`;

            alert(`API调用失败，使用备用方案：\n${fallbackEmail}`);
            hideLoading();
            return fallbackEmail;
        }
    }

    // 创建获取账号按钮
    const buttonId = 'myFloatingActionButton';
    let btn = document.getElementById(buttonId);

    if (!btn) {
        btn = document.createElement('button');
        btn.id = buttonId;
        btn.textContent = '获取账号';
        btn.style.position = 'fixed';
        btn.style.bottom = '100px';
        btn.style.right = '49%';
        btn.style.zIndex = '9999';
        btn.style.padding = '12px 18px';
        btn.style.fontSize = '14px';
        btn.style.backgroundColor = '#007bff';
        btn.style.color = 'white';
        btn.style.border = 'none';
        btn.style.borderRadius = '5px';
        btn.style.cursor = 'pointer';
        btn.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        btn.style.transition = 'background-color 0.3s ease';

        btn.onmouseover = function() { this.style.backgroundColor = '#0056b3'; };
        btn.onmouseout = function() { this.style.backgroundColor = '#007bff'; };

        document.body.appendChild(btn);
    }

    btn.removeEventListener('click', getCode);
    btn.addEventListener('click', getCode);
})();

