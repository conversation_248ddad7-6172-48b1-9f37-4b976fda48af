// ==UserScript==
// @name              augment code tools
// @namespace         http://tampermonkey.net/
// @version           1.7.0
// @description       自动注册 augment 账号
// @icon              https://app.augmentcode.com/favicon.ico
// @name         AutoRegister for augment
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automates registration on augmentcode.com with random email and password
// <AUTHOR> Coding
// @include      *augmentcode.com*
// @grant        none
// ==/UserScript==
(function () {
  function identifier() {
    console.log("执行 identifier 方法");
    
// 自执行函数 - 立即执行
    (async function() {
        try {
            // 调用API获取数据
            const response = await fetch('https://api.12050231.xyz/api/domain');
            
            // 检查响应是否成功
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // 解析JSON数据
            const data = await response.json();
            
            // 获取id为username的input元素
            const usernameInput = document.getElementById('username');
            
            // 检查元素是否存在
            if (!usernameInput) {
                console.error('未找到id为username的input元素');
                return;
            }
            
            // 将message的值填入input标签
            usernameInput.value = data.data || '';
            
            console.log('成功填入值:', data.data);
            
        } catch (error) {
            console.error('填充用户名时发生错误:', error);
            
            // 可以选择在出错时填入默认值
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                usernameInput.value = '获取失败';
            }
        }
    })();
  }

  function passwordless() {
    console.log("执行 passwordless 方法");
    (async function() {
    // 提取当前邮箱
      const emailElement = document.querySelector('.ulp-authenticator-selector-text');
      if (!emailElement) {
          console.error('无法找到邮箱元素');
          return;
      }
      const currentEmail = emailElement.textContent.trim();
      const apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(currentEmail)}&token=E567OUwuWuN`;

      try {
          const response = await fetch(apiUrl);
          const data = await response.json();

          const text = data.text;
          if (!text) {
              console.error('未从接口返回中找到 text 字段');
              return;
          }

          // 提取验证码函数
          function extractVerificationCode(text) {
              // 首先尝试匹配 6 位数字验证码（兼容空格）
              const digitMatch = text.match(/\b\d(?:\s\d){5}\b|\b\d{6}\b/);
              if (digitMatch) {
                  return digitMatch[0].replace(/\s/g, '');
              }

              // 降级：尝试匹配 4-8 位的字母数字组合验证码（兼容空格）
              const alphanumericMatch = text.match(/\b[A-Z0-9](?:\s[A-Z0-9]){3,7}\b|\b[A-Z0-9]{4,8}\b/);
              if (alphanumericMatch) {
                  return alphanumericMatch[0].replace(/\s/g, '');
              }

              return null;
          }

          const code = extractVerificationCode(text);
          if (!code) {
              console.error('未能从文本中提取验证码');
              return;
          }

          // 设置到 input 中
          const inputElement = document.getElementById('code');
          if (!inputElement) {
              console.error('找不到 id 为 code 的 input 元素');
              return;
          }
          inputElement.value = code;
          console.log('验证码已填充:', code);
          // 删除标签
        const labelElement = document.querySelector('[data-dynamic-label-for="code"]');
        if (labelElement) {
            labelElement.remove();
            console.log('已删除 data-dynamic-label-for="code" 的标签');
            // 延迟500毫秒后点击提交按钮
              setTimeout(() => {
                  const submitButton = document.querySelector('button[type="submit"]');
                  if (submitButton) {
                      submitButton.click();
                      console.log('已点击提交按钮');
                  } else {
                      console.warn('未找到提交按钮');
                  }
              }, 1000);
        } else {
            console.warn('未找到需要删除的标签');
        }
      } catch (error) {
          console.error('请求或处理时发生错误:', error);
      }
    })();

  }

  function signin() {
    console.log("执行 signin 方法");
    // 模拟点击 class 为 c-checkbox--mark 的标签
    const checkbox = document.querySelector('.c-checkbox--mark');
    if (checkbox) {
      checkbox.click();
      
      // 2 秒后点击 id 为 signup-button 的标签
      setTimeout(() => {
        const signupButton = document.getElementById('signup-button');
        if (signupButton) {
          signupButton.click();
        } else {
          console.warn('未找到 ID 为 signup-button 的元素');
        }
      }, 1000);
      
    } else {
      console.warn('未找到 class 为 c-checkbox--mark 的元素');
    }

  }


    // 公共方法：生成发号文案
    function generateCopyText(email) {
      return `账号：${email}

官网直接登录（Sign in），然后到 https://767700.xyz/code?token=E567OUwuWuN 自行接收验证码

可以到官网 https://app.augmentcode.com/account/subscription 查询额度
`;
    }
  function team() {
    console.log("执行 team 方法");

    setTimeout(() => {
      const emailEl = document.querySelector('.rt-Text.rt-r-size-2.base-header-email');
      if (emailEl) {
        emailEl.style.cursor = 'pointer';
        emailEl.addEventListener('click', () => {
          const emailText = emailEl.textContent.trim();
          copyToClipboard(generateCopyText(emailText), '发号文案复制成功');
        });
      } else {
        console.warn('未找到邮箱元素');
      }
    }, 3000);
  }


   // 公共方法：复制文本并弹出 toast
    function copyToClipboard(text, toastText = '已复制到剪贴板') {
      navigator.clipboard.writeText(text).then(() => {
        const toast = document.createElement('div');
        toast.textContent = toastText;
        Object.assign(toast.style, {
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: '#000',
          color: '#fff',
          padding: '8px 12px',
          borderRadius: '4px',
          zIndex: '9999'
        });
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
      }).catch(console.error);
    }

  function newteam() {
    // 公共方法：获取随机邮箱并填充到 token-input
    async function fillTokenInput() {
      try {
        const res = await fetch('https://api.12050231.xyz/api/domain');
        const { data } = await res.json();
        const tokenInput = document.querySelector('[data-testid="token-input"]');
        if (tokenInput) tokenInput.innerHTML = data;
        return data;
      } catch (err) {
        console.error('获取邮箱失败:', err);
        return null;
      }
    }

   

    // 公共方法：生成发号文案
    function generateCopyText(email) {
      return `账号：${email}

官网直接登录（Sign in），然后到 https://767700.xyz/code?token=E567OUwuWuN 自行接收验证码。

可以到官网 https://app.augmentcode.com/account/subscription 查询额度
`;
    }

    setTimeout(() => {
      // 1. 给页面按钮绑定点击事件
      const button = document.querySelector('.rt-reset.rt-BaseButton.rt-r-size-2.rt-variant-solid.rt-Button');
      if (button) {
        button.addEventListener('click', async () => {
          const email = await fillTokenInput();
          if (email) copyToClipboard(email, '邮箱已复制到剪贴板');
        });
      }

     
    }, 2000);

     // 2. 创建悬浮按钮
      const floatBtn = document.createElement('button');
      floatBtn.textContent = '复制邮箱';
      Object.assign(floatBtn.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: '9999',
        padding: '10px 16px',
        background: '#007bff',
        color: '#fff',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
        fontSize: '14px'
      });
      document.body.appendChild(floatBtn);

      floatBtn.addEventListener('click', () => {
        const emailEl = document.querySelector('.rt-Text.rt-r-size-2.base-header-email');
        if (emailEl) {
          const emailText = emailEl.textContent.trim();
          copyToClipboard(generateCopyText(emailText), '发号文案复制成功');
        } else {
          console.warn('未找到邮箱元素');
        }
      });
  }

  function windsurf() {
    console.log("执行 windsurf 方法");
    setTimeout(() => {
      const emailEl = document.querySelector('.rt-Text.rt-r-size-2.base-header-email');
      if (emailEl) {
        emailEl.style.cursor = 'pointer';
        emailEl.addEventListener('click', () => {
          const emailText = emailEl.textContent.trim();
          copyToClipboard((emailText), '发号文案复制成功');
        });
      } else {
        console.warn('未找到邮箱元素');
      }
    }, 1500)
  }
  const url = window.location.href;

  function init() {
    if (url.includes("identifier")) {
      identifier();
    } else if (url.includes("passwordless")) {
      setTimeout(() => {
        passwordless();
      }, 2500);
    } else if(url.includes("terms-accept")){
      signin();
    } else if (url.includes("account/subscription")) {
      team();
    }else if (url.includes("account/team")) {
      newteam();
    }else if (url.includes("promotions/windsurf")) {
      windsurf();
    }
  }

    // 页面加载完成后创建 UI
setTimeout(() => {
    init();
}, 500);

  
})();


