// 在这里，可以随便写你的代码，并且，你的代码中
// 1. 可以进行页面上的所有DOM操作
// 2. 可以访问页面上原本已挂载的所有Js变量，比如页面上已经有了jQuery，你可以直接使用
// 3. 可以依赖注入一个第三方js脚本，然后在你的代码中直接使用，如：依赖jQuery后直接使用
// 4. 好了，你的代码可以这样写：

(() => {
	console.log('hello...world...,I am from ',location.href);
  async function autoRegisterProcess() {
    // 获取页面元素
    const emailInput = document.querySelector('input[type="email"]');
    const passwordInput = document.querySelector('input[type="password"]');
    const codeInput = document.querySelector('input[type="text"]');
    const sendCodeButton = document.querySelector('.send-code');
    
    // 检查必要元素是否存在
    if (!emailInput) {
        console.error('未找到 input type="email" 元素');
        return;
    }
    
    if (!passwordInput) {
        console.error('未找到 input type="password" 元素');
        return;
    }
    
    if (!codeInput) {
        console.error('未找到 input type="text" 元素');
        return;
    }
    
    if (!sendCodeButton) {
        console.error('未找到 .send-code 元素');
        return;
    }

    // 生成随机字符串的函数（5位小写字母 + 5位数字）
    function generateRandomString() {
        const alphabet = 'abcdefghijklmnopqrstuvwxyz';
        let letters = '';
        let numbers = '';
        
        for (let i = 0; i < 5; i++) {
            letters += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
        }
        
        for (let i = 0; i < 5; i++) {
            numbers += Math.floor(Math.random() * 10);
        }
        
        return letters + numbers;
    }

    // 生成随机密码的函数（12位大小写字母和数字）
    function generateRandomPassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = '';
        
        for (let i = 0; i < 12; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return password;
    }

    // 触发输入事件的函数
    function triggerInputEvents(element) {
        element.dispatchEvent(new Event('focus', { bubbles: true }));
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        element.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true }));
        element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    }

    // 显示toast通知的函数
    function showToast(message, duration = 0) {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #333;
            color: white;
            padding: 15px 40px 15px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 9999;
            max-width: 400px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            user-select: text;
            position: relative;
        `;
        
        const closeButton = document.createElement('span');
        closeButton.textContent = '×';
        closeButton.style.cssText = `
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 20px;
            cursor: pointer;
            color: #ccc;
            font-weight: bold;
            line-height: 1;
            padding: 5px;
        `;
        
        closeButton.addEventListener('mouseenter', function() {
            this.style.color = '#fff';
        });
        closeButton.addEventListener('mouseleave', function() {
            this.style.color = '#ccc';
        });
        
        const content = document.createElement('div');
        content.textContent = message;
        content.style.cssText = `
            cursor: pointer;
            padding-right: 10px;
        `;
        
        toast.appendChild(content);
        toast.appendChild(closeButton);
        document.body.appendChild(toast);
        
        content.addEventListener('click', function() {
            const range = document.createRange();
            range.selectNodeContents(content);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        });
        
        closeButton.addEventListener('click', function() {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
        
        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, duration);
        }
    }

    // 域名数组
    const domains = [
        "001927.xyz", "002627.xyz", "005626.xyz", "005673.xyz", "007772.xyz",
        "007773.xyz", "007775.xyz", "007778.xyz", "007779.xyz", "008756.xyz",
        "009567.xyz", "20231131.xyz", "202504137.xyz", "20251727.xyz", "20252525.xyz",
        "20262626.xyz", "20272727.xyz", "212200.xyz", "252600.xyz", "262700.xyz",
        "26272829.xyz", "267000.xyz", "272800.xyz", "27282828.xyz", "276000.xyz",
        "286000.xyz", "293000.xyz", "367000.xyz", "372000.xyz", "386000.xyz",
        "676800.xyz", "697000.xyz", "707100.xyz", "717200.xyz", "725000.xyz",
        "763000.xyz", "767700.xyz", "785000.xyz", "821000.xyz", "873000.xyz",
        "827000.xyz", "996787.xyz", "996976.xyz"
    ];

    try {
        // 生成随机邮箱和密码
        const randomDomain = domains[Math.floor(Math.random() * domains.length)];
        const generatedEmail = generateRandomString() + '@' + randomDomain;
        const generatedPassword = generateRandomPassword();

        // 填入邮箱
        emailInput.value = generatedEmail;
        emailInput.focus();
        triggerInputEvents(emailInput);
        console.log('生成的邮箱:', generatedEmail);

        // 填入密码
        passwordInput.value = generatedPassword;
        passwordInput.focus();
        triggerInputEvents(passwordInput);
        console.log('生成的密码:', generatedPassword);

        // 短暂延迟确保事件处理完成
        setTimeout(() => {
            if (emailInput.value !== generatedEmail) {
                emailInput.value = generatedEmail;
            }
            emailInput.focus();
            triggerInputEvents(emailInput);
            
            console.log('点击发送验证码按钮...');
            sendCodeButton.click();
        }, 100);

        // 等待5秒后获取邮箱内容
        console.log('等待5秒后获取邮箱内容...');
        setTimeout(async () => {
            try {
                console.log('点击邮箱输入框获取焦点...');
                emailInput.click();
                emailInput.focus();
                
                const currentEmail = emailInput.value;
                const response = await fetch(`https://email.767700.xyz/?email=${currentEmail}&token=E567OUwuWuN`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('邮箱内容:', data);
                
                // 提取验证码（匹配类似 GYEB5B 的格式：4个大写字母+1位数字+1个大写字母）
                const match = data.text.match(/\b[A-Z0-9]{4,8}\b/);
                const verificationCode = match ? match[0] : null;
                
                if (verificationCode) {
                    console.log('提取到的验证码:', verificationCode);
                    // 填入验证码
                    codeInput.value = verificationCode;
                    codeInput.focus();
                    triggerInputEvents(codeInput);
                } else {
                    console.error('未找到符合格式的验证码');
                }
                
                // 拼接账号信息字符串
                const currentPassword = passwordInput.value;
                const accountInfo = `账号：${currentEmail}\n密码：${currentPassword}\n虚拟账号，售出不退哦，登录后官网 https://www.trae.ai/account-setting#usage 可查独享额度 \n防失联客服：https://767700.xyz/kefu.jpg`;
                
                try {
                    await navigator.clipboard.writeText(accountInfo);
                    console.log('账号信息已复制到剪贴板');
                } catch (clipboardError) {
                    console.error('复制到剪贴板失败:', clipboardError);
                }
                
                showToast(accountInfo);
                
            } catch (error) {
                console.error('获取邮箱内容失败:', error);
                
                const currentEmail = emailInput.value || '生成失败';
                const currentPassword = passwordInput.value || '生成失败';
                const accountInfo = `账号：${currentEmail}\n密码：${currentPassword}\n虚拟账号，售出不退哦，登录后官网 https://www.trae.ai/account-setting#usage 可查独享额度 \n防失联客服：https://767700.xyz/kefu.jpg`;
                
                try {
                    await navigator.clipboard.writeText(accountInfo);
                    console.log('账号信息已复制到剪贴板');
                } catch (clipboardError) {
                    console.error('复制到剪贴板失败:', clipboardError);
                }
                
                showToast(accountInfo);
            }
        }, 5000);

    } catch (error) {
        console.error('执行过程中发生错误:', error);
        
        const currentEmail = emailInput.value || '生成失败';
        const currentPassword = passwordInput.value || '生成失败';
        const accountInfo = `账号：${currentEmail}\n密码：${currentPassword}\n虚拟账号，售出不退哦，登录后官网 https://www.trae.ai/account-setting#usage 可查独享额度 \n防失联客服：https://767700.xyz/kefu.jpg`;
        
        try {
            await navigator.clipboard.writeText(accountInfo);
            console.log('账号信息已复制到剪贴板');
        } catch (clipboardError) {
            console.error('复制到剪贴板失败:', clipboardError);
        }
        
        showToast(accountInfo);
    }
}

  setTimeout(() => {
    autoRegisterProcess();
}, 1500);
  

})();

