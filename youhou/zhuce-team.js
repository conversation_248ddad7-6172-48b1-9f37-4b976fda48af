// ==UserScript==
// @name              augment code tools
// @namespace         http://tampermonkey.net/
// @version           1.7.0
// @description       自动注册 augment 账号
// @icon              https://app.augmentcode.com/favicon.ico
// @name         AutoRegister for augment
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automates registration on augmentcode.com with random email and password
// <AUTHOR> Coding
// @include      *augmentcode.com*
// @grant        none
// ==/UserScript==
(function () {
  function identifier() {
    console.log("执行 identifier 方法");
    
    // 自执行函数 - 立即执行
    (async function() {
        try {
            // 从剪贴板读取内容
            const clipboardContent = await navigator.clipboard.readText();
            
            // 获取id为username的input元素
            const usernameInput = document.getElementById('username');
            
            // 检查元素是否存在
            if (!usernameInput) {
                console.error('未找到id为username的input元素');
                return;
            }
            
            // 将剪贴板内容填入input标签
            usernameInput.value = clipboardContent || '';
            
            console.log('成功填入剪贴板内容:', clipboardContent);
            
        } catch (error) {
            console.error('读取剪贴板时发生错误:', error);
            
            // 出错时填入默认值
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                usernameInput.value = '';
            }
        }
    })();
  }

  function passwordless() {
    console.log("执行 passwordless 方法");
    (async function() {
    // 提取当前邮箱
      const emailElement = document.querySelector('.ulp-authenticator-selector-text');
      if (!emailElement) {
          console.error('无法找到邮箱元素');
          return;
      }
      const currentEmail = emailElement.textContent.trim();
      const apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(currentEmail)}&token=E567OUwuWuN`;

      try {
          const response = await fetch(apiUrl);
          const data = await response.json();

          const text = data.text;
          if (!text) {
              console.error('未从接口返回中找到 text 字段');
              return;
          }

          // 提取验证码函数
          function extractVerificationCode(text) {
              // 首先尝试匹配 6 位数字验证码（兼容空格）
              const digitMatch = text.match(/\b\d(?:\s\d){5}\b|\b\d{6}\b/);
              if (digitMatch) {
                  return digitMatch[0].replace(/\s/g, '');
              }

              // 降级：尝试匹配 4-8 位的字母数字组合验证码（兼容空格）
              const alphanumericMatch = text.match(/\b[A-Z0-9](?:\s[A-Z0-9]){3,7}\b|\b[A-Z0-9]{4,8}\b/);
              if (alphanumericMatch) {
                  return alphanumericMatch[0].replace(/\s/g, '');
              }

              return null;
          }

          const code = extractVerificationCode(text);
          if (!code) {
              console.error('未能从文本中提取验证码');
              return;
          }

          // 设置到 input 中
          const inputElement = document.getElementById('code');
          if (!inputElement) {
              console.error('找不到 id 为 code 的 input 元素');
              return;
          }
          inputElement.value = code;
          console.log('验证码已填充:', code);
          
          // 删除标签
          const labelElement = document.querySelector('[data-dynamic-label-for="code"]');
          if (labelElement) {
              labelElement.remove();
              console.log('已删除 data-dynamic-label-for="code" 的标签');
              
              // 延迟500毫秒后点击提交按钮
              setTimeout(() => {
                  const submitButton = document.querySelector('button[type="submit"]');
                  if (submitButton) {
                      submitButton.click();
                      console.log('已点击提交按钮');
                  } else {
                      console.warn('未找到提交按钮');
                  }
              }, 500);
          } else {
              console.warn('未找到需要删除的标签');
          }
      } catch (error) {
          console.error('请求或处理时发生错误:', error);
      }
    })();
  }

  function signin() {
    console.log("执行 signin 方法");
    
    // 首先检查是否有 gsi-material-button-content-wrapper 元素
    const googleButton = document.querySelector('.gsi-material-button-content-wrapper');
    if (googleButton) {
      console.log('找到 Google 登录按钮');
      
      // 检查是否存在 terms-of-service-checkbox 标签
      const termsCheckbox = document.querySelector('[for="terms-of-service-checkbox"]');
      if (termsCheckbox) {
        console.log('找到服务条款复选框，正在点击...');
        termsCheckbox.click();
        
        // 延迟500毫秒后点击 Google 按钮
        setTimeout(() => {
          console.log('正在点击 Google 登录按钮...');
          googleButton.click();
        }, 500);
      } else {
        // 如果没有找到服务条款复选框，直接点击 Google 按钮
        console.log('未找到服务条款复选框，直接点击 Google 登录按钮...');
        googleButton.click();
      }
      return;
    }
    
    // 原有的逻辑 - 如果没有 Google 按钮，继续执行原来的流程
    const checkbox = document.querySelector('.c-checkbox--mark');
    if (checkbox) {
      checkbox.click();
      
      // 2 秒后点击 id 为 signup-button 的标签
      setTimeout(() => {
        const signupButton = document.getElementById('signup-button');
        if (signupButton) {
          signupButton.click();
        } else {
          console.warn('未找到 ID 为 signup-button 的元素');
        }
      }, 1000);
      
    } else {
      console.warn('未找到 class 为 c-checkbox--mark 的元素');
    }
  }

  // 公共方法：生成发号文案
    function generateCopyText(email) {
      return `账号：${email}

官网直接登录（Sign in），然后到 https://767700.xyz/code?token=E567OUwuWuN 自行接收验证码。虚拟商品，非账号问题售出不退哦。

可以到官网 https://app.augmentcode.com/account/subscription 查询额度

`;
    }

    // 公共方法：复制文本并弹出 toast
    function copyToClipboard(text, toastText = '已复制到剪贴板') {
      navigator.clipboard.writeText(text).then(() => {
        const toast = document.createElement('div');
        toast.textContent = toastText;
        Object.assign(toast.style, {
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: '#000',
          color: '#fff',
          padding: '8px 12px',
          borderRadius: '4px',
          zIndex: '9999'
        });
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
      }).catch(console.error);
    }

  function team() {
    console.log("执行 team 方法");

    setTimeout(() => {
      const emailEl = document.querySelector('.rt-Text.rt-r-size-2.base-header-email');
      if (emailEl) {
        emailEl.style.cursor = 'pointer';
        emailEl.addEventListener('click', () => {
          const emailText = emailEl.textContent.trim();
          copyToClipboard(generateCopyText(emailText), '发号文案复制成功');
        });
      } else {
        console.warn('未找到邮箱元素');
      }
    }, 1500);
    
    
  }

  function newteam() {
    // 公共方法：获取随机邮箱并填充到 token-input
    async function fillTokenInput() {
      try {
        const res = await fetch('https://api.12050231.xyz/api/domain');
        const { data } = await res.json();
        const tokenInput = document.querySelector('[data-testid="token-input"]');
        if (tokenInput) tokenInput.innerHTML = data;
        return data;
      } catch (err) {
        console.error('获取邮箱失败:', err);
        return null;
      }
    }

    

    

    setTimeout(() => {
      // 1. 给页面按钮绑定点击事件
      const button = document.querySelector('.rt-reset.rt-BaseButton.rt-r-size-2.rt-variant-solid.rt-Button');
      if (button) {
        button.addEventListener('click', async () => {
          const email = await fillTokenInput();
          if (email) copyToClipboard(email, '邮箱已复制到剪贴板');
        });
      }

     
    }, 2000);


  }

  function invitations() {
    console.log("执行 invitations 方法");
    
    // 查找并点击 accept-button
    setTimeout(() => {
      const acceptButton = document.querySelector('.accept-button');
      if (acceptButton) {
        acceptButton.click();
        console.log('已点击接受邀请按钮');
      } else {
        console.warn('未找到 class 为 accept-button 的按钮');
      }
    }, 1000); // 延迟1秒确保页面加载完成
  }

  const url = window.location.href;

  function init() {
    if (url.includes("identifier")) {
      identifier();
    } else if (url.includes("passwordless")) {
      setTimeout(() => {
        passwordless();
      }, 2000);
    } else if(url.includes("terms-accept")){
      if (url.includes("invitations")) {
        invitations();
      } else {
        signin();
      }
    } else if (url.includes("account/subscription")) {
      team();
    }else if (url.includes("account/team")) {
      newteam();
    } 
  }

    // 页面加载完成后创建 UI
setTimeout(() => {
    init();
}, 500);

  
})();


